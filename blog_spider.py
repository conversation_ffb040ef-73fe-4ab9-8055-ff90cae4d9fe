import requests
from bs4 import BeautifulSoup
import asyncio, aiohttp



# urls = [
#     f"https://www.cnblogs.com/#p{i}" for i in range(1, 50 + 1)
# ]
urls = [
    f"https://www.cnblogs.com/sitehome/p/{i}" for i in range(1, 50 + 1)
]


def crawler(url):
    r = requests.get(url)
    print((url, len(r.text)))
    return r.text


# post-item-title
def parser(html):
    soup = BeautifulSoup(html, 'html.parser')
    links = soup.find_all('a', class_='post-item-title')
    ret = [(link['href'], link.get_text()) for link in links]
    return ret
#异步
async def fetch(url, session):
    async with session.get(url) as response:
        html = await response.text()
        print((url, len(html)))
        return html
async def asparser(html):
    soup = BeautifulSoup(html, 'html.parser')
    links = soup.find_all('a', class_='post-item-title')
    ret = [(link['href'], link.get_text()) for link in links]
    return ret

async def main():
    async with aiohttp.ClientSession() as session:
        htmls = [fetch(url, session) for url in urls]
        htmls = await asyncio.gather(*htmls)
        for html in htmls:
            ret = await asparser(html)
            print(ret)


async def crawler_async():
    urls = [f"https://www.cnblogs.com/#p{i}" for i in range(1, 50 + 1)]

    async with aiohttp.ClientSession() as session:
        tasks = [fetch(url, session) for url in urls]
        #
        html_results = await asyncio.gather(*tasks) #这个上面意思

        # parse_tasks = [parser(html) for html in html_results]
        # results = await asyncio.gather(*parse_tasks)

        # Flatten the results list
        # all_links = []
        # for links in results:
        #     all_links.extend(links)
        #
        # return all_links

if __name__ == '__main__':
    print(parser(crawler(urls[0])))