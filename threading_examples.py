"""
Python 线程和线程池详解示例
包含基础线程、线程池、跨线程通信等实例
"""

import threading
import time
import queue
import concurrent.futures
from threading import Thread, Lock, Event, Condition, Semaphore
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(threadName)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 1. 基础线程示例
# ============================================================================

def basic_thread_example():
    """基础线程使用示例"""
    print("\n=== 基础线程示例 ===")
    
    def worker(name, delay):
        """工作函数"""
        logger.info(f"Worker {name} 开始工作")
        time.sleep(delay)
        logger.info(f"Worker {name} 完成工作")
    
    # 创建并启动线程
    threads = []
    for i in range(3):
        t = Thread(target=worker, args=(f"Thread-{i}", i + 1))
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    print("所有线程完成")

# ============================================================================
# 2. 线程池示例
# ============================================================================

def thread_pool_example():
    """线程池使用示例"""
    print("\n=== 线程池示例 ===")
    
    def task(n):
        """任务函数"""
        logger.info(f"处理任务 {n}")
        time.sleep(1)
        return n * n
    
    # 使用ThreadPoolExecutor
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # 提交任务
        futures = [executor.submit(task, i) for i in range(5)]
        
        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            logger.info(f"任务结果: {result}")

def thread_pool_map_example():
    """线程池map方法示例"""
    print("\n=== 线程池map方法示例 ===")
    
    def square(n):
        time.sleep(0.5)
        return n * n
    
    numbers = [1, 2, 3, 4, 5]
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        results = list(executor.map(square, numbers))
        print(f"结果: {results}")

# ============================================================================
# 3. 跨线程通信示例
# =========================== =================================================

def queue_communication_example():
    """使用Queue进行跨线程通信"""
    print("\n=== Queue跨线程通信示例 ===")
    
    # 创建队列
    task_queue = queue.Queue()
    result_queue = queue.Queue()
    
    def producer():
        """生产者"""
        for i in range(5):
            task_queue.put(f"任务-{i}")
            logger.info(f"生产任务-{i}")
            time.sleep(0.5)
        
        # 发送结束信号
        task_queue.put(None)
    
    def consumer():
        """消费者"""
        while True:
            task = task_queue.get()
            if task is None:
                break
            
            logger.info(f"处理 {task}")
            result = f"{task}-完成"
            result_queue.put(result)
            time.sleep(1)
            task_queue.task_done()
    
    # 启动线程
    producer_thread = Thread(target=producer)
    consumer_thread = Thread(target=consumer)
    
    producer_thread.start()
    consumer_thread.start()
    
    producer_thread.join()
    consumer_thread.join()
    
    # 获取结果
    while not result_queue.empty():
        result = result_queue.get()
        logger.info(f"结果: {result}")

def event_communication_example():
    """使用Event进行线程同步"""
    print("\n=== Event线程同步示例 ===")
    
    event = Event()
    
    def waiter():
        """等待者"""
        logger.info("等待事件...")
        event.wait()
        logger.info("事件已触发，继续执行")
    
    def setter():
        """设置者"""
        time.sleep(2)
        logger.info("设置事件")
        event.set()
    
    # 启动线程
    waiter_thread = Thread(target=waiter)
    setter_thread = Thread(target=setter)
    
    waiter_thread.start()
    setter_thread.start()
    
    waiter_thread.join()
    setter_thread.join()

def condition_example():
    """使用Condition进行复杂同步"""
    print("\n=== Condition同步示例 ===")
    
    condition = Condition()
    items = []
    
    def consumer(name):
        """消费者"""
        with condition:
            while len(items) == 0:
                logger.info(f"消费者 {name} 等待...")
                condition.wait()
            
            item = items.pop(0)
            logger.info(f"消费者 {name} 消费了 {item}")
    
    def producer():
        """生产者"""
        for i in range(3):
            time.sleep(1)
            with condition:
                item = f"item-{i}"
                items.append(item)
                logger.info(f"生产了 {item}")
                condition.notify_all()
    
    # 启动线程
    consumers = [Thread(target=consumer, args=(f"C{i}",)) for i in range(2)]
    producer_thread = Thread(target=producer)
    
    for c in consumers:
        c.start()
    producer_thread.start()
    
    for c in consumers:
        c.join()
    producer_thread.join()

# ============================================================================
# 4. 线程安全示例
# ============================================================================

def thread_safety_example():
    """线程安全示例"""
    print("\n=== 线程安全示例 ===")
    
    # 不安全的计数器
    class UnsafeCounter:
        def __init__(self):
            self.count = 0
        
        def increment(self):
            for _ in range(100000):
                self.count += 1
    
    # 安全的计数器
    class SafeCounter:
        def __init__(self):
            self.count = 0
            self.lock = Lock()
        
        def increment(self):
            with self.lock:
                for _ in range(100000):
                    self.count += 1
    
    # 测试不安全的计数器
    unsafe_counter = UnsafeCounter()
    threads = [Thread(target=unsafe_counter.increment) for _ in range(2)]
    
    for t in threads:
        t.start()
    for t in threads:
        t.join()
    
    print(f"不安全计数器结果: {unsafe_counter.count} (期望: 200000)")
    
    # 测试安全的计数器
    safe_counter = SafeCounter()
    threads = [Thread(target=safe_counter.increment) for _ in range(2)]
    
    for t in threads:
        t.start()
    for t in threads:
        t.join()
    
    print(f"安全计数器结果: {safe_counter.count} (期望: 200000)")

def semaphore_example():
    """信号量示例 - 限制并发访问"""
    print("\n=== 信号量示例 ===")
    
    # 限制最多2个线程同时访问资源
    semaphore = Semaphore(2)
    
    def access_resource(worker_id):
        """访问受限资源"""
        with semaphore:
            logger.info(f"Worker {worker_id} 获得资源访问权限")
            time.sleep(2)  # 模拟资源使用
            logger.info(f"Worker {worker_id} 释放资源")
    
    # 创建5个工作线程
    threads = [Thread(target=access_resource, args=(i,)) for i in range(5)]
    
    for t in threads:
        t.start()
    for t in threads:
        t.join()

# ============================================================================
# 5. 生产者-消费者模式
# ============================================================================

def producer_consumer_example():
    """完整的生产者-消费者示例"""
    print("\n=== 生产者-消费者模式示例 ===")
    
    buffer = queue.Queue(maxsize=3)  # 缓冲区大小为3
    
    def producer(producer_id):
        """生产者"""
        for i in range(5):
            item = f"P{producer_id}-Item{i}"
            buffer.put(item)
            logger.info(f"生产者 {producer_id} 生产了 {item}")
            time.sleep(0.5)
    
    def consumer(consumer_id):
        """消费者"""
        while True:
            try:
                item = buffer.get(timeout=3)
                logger.info(f"消费者 {consumer_id} 消费了 {item}")
                time.sleep(1)
                buffer.task_done()
            except queue.Empty:
                logger.info(f"消费者 {consumer_id} 超时退出")
                break
    
    # 创建生产者和消费者线程
    producers = [Thread(target=producer, args=(i,)) for i in range(2)]
    consumers = [Thread(target=consumer, args=(i,)) for i in range(2)]
    
    # 启动所有线程
    for p in producers:
        p.start()
    for c in consumers:
        c.start()
    
    # 等待生产者完成
    for p in producers:
        p.join()
    
    # 等待队列清空
    buffer.join()
    
    # 消费者会自动超时退出
    for c in consumers:
        c.join()

# ============================================================================
# 主函数
# ============================================================================

def main():
    """运行所有示例"""
    print("Python 线程和线程池示例集合")
    print("=" * 50)
    
    # 运行所有示例
    basic_thread_example()
    thread_pool_example()
    thread_pool_map_example()
    queue_communication_example()
    event_communication_example()
    condition_example()
    thread_safety_example()
    semaphore_example()
    producer_consumer_example()

if __name__ == "__main__":
    main()
