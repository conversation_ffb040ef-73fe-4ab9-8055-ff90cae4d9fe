# Python 并发编程完整指南

这个项目包含了Python并发编程的全面示例，涵盖线程、线程池、跨线程通信以及处理不支持跨线程库（如Playwright）的解决方案。

## 📁 文件结构

```
ant_learn_python_concurrent/
├── threading_examples.py              # 基础线程和线程池示例
├── playwright_threading_solution.py   # Playwright跨线程解决方案
├── advanced_threading_patterns.py     # 高级线程模式和最佳实践
├── requirements.txt                   # 项目依赖
└── README.md                          # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行基础示例

```bash
python threading_examples.py
```

### 3. 运行Playwright解决方案

```bash
python playwright_threading_solution.py
```

### 4. 运行高级模式示例

```bash
python advanced_threading_patterns.py
```

## 📚 内容概览

### 1. 基础线程示例 (`threading_examples.py`)

- **基础线程使用**: Thread类的创建和管理
- **线程池**: ThreadPoolExecutor的使用和最佳实践
- **跨线程通信**: Queue、Event、Condition等同步原语
- **线程安全**: Lock、Semaphore等同步机制
- **生产者-消费者模式**: 经典并发模式实现

#### 主要示例：

```python
# 基础线程
def worker(name, delay):
    print(f"Worker {name} 开始工作")
    time.sleep(delay)
    print(f"Worker {name} 完成工作")

t = Thread(target=worker, args=("Thread-1", 2))
t.start()
t.join()

# 线程池
with ThreadPoolExecutor(max_workers=3) as executor:
    futures = [executor.submit(task, i) for i in range(5)]
    results = [future.result() for future in futures]
```

### 2. Playwright跨线程解决方案 (`playwright_threading_solution.py`)

由于Playwright不支持跨线程使用，提供了三种解决方案：

#### 方案1: 单线程工作器模式
- 在专用线程中运行Playwright
- 通过队列进行任务通信
- 适合简单的爬取任务

```python
worker = PlaywrightWorker()
worker.start()

# 在其他线程中使用
result = worker.execute_task(scrape_function, url)
```

#### 方案2: 异步管理器模式
- 使用asyncio管理多个Playwright实例
- 支持并发处理多个任务
- 适合高并发场景

```python
async with AsyncPlaywrightManager(max_workers=3) as manager:
    tasks = [manager.submit_task(func, url) for url in urls]
    results = await asyncio.gather(*tasks)
```

#### 方案3: 进程池模式（推荐）
- 每个进程独立运行Playwright实例
- 完全避免线程安全问题
- 适合CPU密集型任务

```python
process_pool = PlaywrightProcessPool(num_processes=2)
process_pool.start()
result = process_pool.execute_task('scrape', url)
```

### 3. 高级线程模式 (`advanced_threading_patterns.py`)

- **线程本地存储**: 每个线程独立的数据存储
- **线程装饰器**: 简化线程使用的装饰器
- **上下文管理器**: 自动管理线程资源
- **线程池监控**: 实时监控线程池状态
- **线程安全单例**: 多线程环境下的单例模式

#### 高级特性示例：

```python
# 线程装饰器
@threaded
def background_task(name, duration):
    time.sleep(duration)
    print(f"Task {name} completed")

# 同步装饰器
@synchronized()
def thread_safe_function():
    # 线程安全的操作
    pass

# 上下文管理器
with ThreadPoolContext(max_workers=4) as pool:
    pool.submit(task_function, args)
```

## 🔧 核心概念详解

### 线程 vs 进程

| 特性 | 线程 | 进程 |
|------|------|------|
| 内存共享 | 共享内存空间 | 独立内存空间 |
| 创建开销 | 低 | 高 |
| 通信方式 | 共享变量、队列 | IPC、管道、队列 |
| 故障隔离 | 差（一个线程崩溃影响整个进程） | 好（进程间相互独立） |
| 适用场景 | I/O密集型任务 | CPU密集型任务 |

### 同步原语选择指南

- **Lock**: 最基本的互斥锁，保护共享资源
- **RLock**: 可重入锁，同一线程可多次获取
- **Semaphore**: 限制同时访问资源的线程数量
- **Event**: 简单的线程间信号通信
- **Condition**: 复杂的条件等待和通知
- **Queue**: 线程安全的数据交换

### 最佳实践

1. **避免死锁**:
   - 始终以相同顺序获取多个锁
   - 使用超时机制
   - 尽量减少锁的持有时间

2. **性能优化**:
   - 根据任务类型选择线程或进程
   - 合理设置线程池大小
   - 避免频繁创建销毁线程

3. **错误处理**:
   - 在线程中捕获异常
   - 使用日志记录线程状态
   - 实现优雅的关闭机制

## 🛠️ 实际应用场景

### 1. Web爬虫
```python
# 多线程爬取网页
with ThreadPoolExecutor(max_workers=10) as executor:
    futures = [executor.submit(scrape_url, url) for url in urls]
    results = [future.result() for future in futures]
```

### 2. 文件处理
```python
# 并行处理多个文件
def process_file(filename):
    with open(filename, 'r') as f:
        # 处理文件内容
        pass

with ThreadPoolExecutor() as executor:
    executor.map(process_file, file_list)
```

### 3. API请求
```python
# 并发API调用
import requests

def api_call(endpoint):
    response = requests.get(endpoint)
    return response.json()

with ThreadPoolExecutor(max_workers=5) as executor:
    results = list(executor.map(api_call, endpoints))
```

## ⚠️ 常见陷阱和解决方案

### 1. GIL (Global Interpreter Lock)
- **问题**: Python的GIL限制了真正的并行执行
- **解决**: 对于CPU密集型任务使用multiprocessing

### 2. 资源竞争
- **问题**: 多线程访问共享资源导致数据不一致
- **解决**: 使用适当的同步原语

### 3. 内存泄漏
- **问题**: 线程未正确清理导致内存泄漏
- **解决**: 使用上下文管理器和try-finally块

### 4. 第三方库线程安全
- **问题**: 某些库（如Playwright）不支持跨线程
- **解决**: 使用本项目提供的解决方案

## 🔍 调试和监控

### 1. 线程状态监控
```python
import threading
print(f"活跃线程数: {threading.active_count()}")
for thread in threading.enumerate():
    print(f"线程: {thread.name}, 存活: {thread.is_alive()}")
```

### 2. 性能分析
```python
import cProfile
cProfile.run('your_threaded_function()')
```

### 3. 内存监控
```python
import psutil
import os

process = psutil.Process(os.getpid())
print(f"内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB")
```

## 📖 进阶学习

1. **异步编程**: 学习asyncio和async/await
2. **并发模式**: 研究Actor模型、CSP等并发模式
3. **分布式计算**: 了解Celery、Dask等分布式框架
4. **性能优化**: 学习Cython、NumPy等高性能库

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
