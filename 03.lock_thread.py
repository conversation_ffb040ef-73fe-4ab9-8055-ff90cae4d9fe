import queue
import threading
import time
lock = threading.Lock()

class Account():
    def __init__(self,account):
        self.account = account


def draw(account,draw_amount):
    with lock:
        if account.account >= draw_amount:
            time.sleep(0.1)
            print(f'余额充足，正在从{account.account}取出{draw_amount}元')
            account.account -= draw_amount
            print(f'余额为{account.account}')
        else:
            print('余额不足')



if __name__ == '__main__':
    t = Account(1000)
    ta = threading.Thread(target=draw,args=(t,800))
    tb = threading.Thread(target=draw,args=(t,800))
    ta.start()
    tb.start()