('https://www.cnblogs.com/johnnyzen/p/18949896', '[Java/字节流/BytesReader] 核心源码精讲: ByteArrayInputStream(字节数组输入流，JDK 1.0-)')
('https://www.cnblogs.com/ThinkerQAQ/p/18949737', 'Java源码分析系列笔记-12.BlockingQueue')
('https://www.cnblogs.com/cj8988/p/18949573', '搭建一个图片变视频的AI(二)：开始搭建')
('https://www.cnblogs.com/cmt/p/18948571', '博客园众包平台：诚征3D影像景深延拓实时处理方案（预算8-15万）')
('https://www.cnblogs.com/somefuture/p/18949630', 'GIM 1.4 发布了 （附使用 mkdocs 快速制作静态站点流程）')
('https://www.cnblogs.com/yupi/p/18949623', 'Spring AI Alibaba 1.0 正式发布！核心特性速览+老项目升级指南')
('https://www.cnblogs.com/vivotech/p/18949533', '三方系统集成（低代码）平台实践')
('https://www.cnblogs.com/huangxincheng/p/18949512', 'DotTrace系列：5. 诊断程序的 慢File 和 慢SQL')
('https://www.cnblogs.com/linx/p/18949429', 'Web前端入门第 71 问：JavaScript DOM 节点操作（增删改）常用方法')
('https://www.cnblogs.com/chenyishi/p/18949397', 'pytorch入门 - GoogLeNet神经网络')
('https://www.cnblogs.com/MeteorSeed/p/18947523', '别让理论成为“紧箍咒”！打破开发教条主义做正确的软件')
('https://www.cnblogs.com/powertoolsteam/p/18949333', '使用Spread控件构建Checkbook工程的技术指南')
('https://www.cnblogs.com/du-hong/p/18441675', '《刚刚问世》系列初窥篇-Java+Playwright自动化测试-20- 操作鼠标拖拽 - 上篇（详细教程）')
('https://www.cnblogs.com/youlanjihua/p/18949219', 'HarmonyOS NEXT仓颉开发语言实战案例：银行App')
('https://www.cnblogs.com/Can-daydayup/p/18948939', '使用 xUnit 快速编写 .NET 应用单元测试')
('https://www.cnblogs.com/hunterxiong/p/18949074', 'Golang基础笔记四之map')
('https://www.cnblogs.com/Nuwa/p/18949052', '使用redis的stream数据类型做消息队列')
('https://www.cnblogs.com/lmy5215006/p/18919081', '为什么说方法的参数最好不要超过4个？')
('https://www.cnblogs.com/bricheersz/p/18948960', '你应该懂的AI大模型（八）之 微调 之 增量微调')
('https://www.cnblogs.com/OBCE666/p/18948934', 'OceanBase向量检索在货拉拉的探索和实践')
('https://www.cnblogs.com/ThinkerQAQ/p/18948910', 'Java源码分析系列笔记-10.CopyOnWriteArrayList')
('https://www.cnblogs.com/dayue-bc/p/18948762', 'Spring Cloud微服务架构深度解析')
('https://www.cnblogs.com/zhaloe/p/18946864', 'ArkUI-X通过Stage模型开发Android端应用指南(二)')
('https://www.cnblogs.com/Naylor/p/18948402', '探索 JavaCV：开启计算机视觉与多媒体处理新世界')
('https://www.cnblogs.com/noear/p/18948181', 'Solon Flow：轻量级流程编排引擎，让业务逻辑更优雅')
('https://www.cnblogs.com/powertoolsteam/p/18948104', '使用 Spread.net将 Excel 中的文本拆分为多段')
('https://www.cnblogs.com/ThinkerQAQ/p/18947950', 'Java源码分析系列笔记-9.CountDownLatch')
('https://www.cnblogs.com/du-hong/p/18438871', '《刚刚问世》系列初窥篇-Java+Playwright自动化测试-19- 操作鼠标悬停（详细教程）')
('https://www.cnblogs.com/huangxincheng/p/18947774', 'DotTrace系列：4. 诊断窗体程序变卡之原因分析')
('https://www.cnblogs.com/BNTang/p/18947708', 'Cursor生成UI，加一步封神')
('https://www.cnblogs.com/yanyan-yanyan/p/18947199', '一文掌握 HarmonyOS5 模拟器与真机调试技巧')
('https://www.cnblogs.com/lesliexin/p/18923109', '[原创]《C#高级GDI+实战：从零开发一个流程图》第04章：来个圆形，连线它！')
('https://www.cnblogs.com/mingupupu/p/18947524', '适用于编程小白的Python学习01：Pandas初探')
('https://www.cnblogs.com/mjunz/p/18947436', '商品中心—13.商品卖家系统的高并发文档')
('https://www.cnblogs.com/linx/p/18947426', 'Web前端入门第 70 问：JavaScript DOM 节点查找常用方法')
('https://www.cnblogs.com/anding/p/18942909', 'C#.Net筑基-优雅LINQ的查询艺术')
('https://www.cnblogs.com/ThinkerQAQ/p/18947211', 'Java源码分析系列笔记-8.CyclicBarrier')
('https://www.cnblogs.com/shanyou/p/18947206', 'Dapr Conversation 构建块')
('https://www.cnblogs.com/longronglang/p/18947182', '【Playwright + Python】系列（十七）揭秘 Playwright 处理 Handles：开启高效自动化之门')
('https://www.cnblogs.com/xuxueli/p/18947083', 'XXL-JOB v3.1.1 | 分布式任务调度平台（Dify工作流调度增强）')
('https://www.cnblogs.com/sun-10387834/p/18951442', '【设计模式】外观模式')
('https://www.cnblogs.com/linx/p/18951405', 'Web前端入门第 72 问：JavaScript DOM 内容操作常用方法和 XSS 注入攻击')
('https://www.cnblogs.com/powertoolsteam/p/18951368', '使用GcExcel .NET将Excel导出为PDF')
('https://www.cnblogs.com/xueweihan/p/18951201', '《HelloGitHub》第 111 期')
('https://www.cnblogs.com/zengzuo613/p/18943037', 'MySQL 字符集、排序规则与查询关系详解')
('https://www.cnblogs.com/gogoSandy/p/18951293', 'ES索引迁移优化：3倍速ReIndex + 零感知切换')
('https://www.cnblogs.com/cmt/p/18951028', '发布一个小功能，通过 markdown 图片语法嵌入B站视频')
('https://www.cnblogs.com/hunterxiong/p/18951127', 'Golang基础笔记五之结构体')
('https://www.cnblogs.com/www-htz-pw/p/18950922/ora01555xi-lie-yiora01555cuo-wu-de-ben-zhi-yu-yuan', 'ORA-01555系列：一、ORA-01555错误的本质与原理')
('https://www.cnblogs.com/dayue-bc/p/18950872', 'Spring Cloud Alibaba 微服务架构深度解析')
('https://www.cnblogs.com/mjunz/p/18950836', '商品中心—14.库存分桶初始化的技术文档')
('https://www.cnblogs.com/yupi/p/18950823', '谷歌新AI工具杀疯了？免费，但有点坑！Gemini CLI 抢先实测')
('https://www.cnblogs.com/ThinkerQAQ/p/18950788', 'Java源码分析系列笔记-14.ThreadPool')
('https://www.cnblogs.com/zcry/p/18945014', '一个程序在计算机中是如何运行的？')
('https://www.cnblogs.com/BNTang/p/18950545', '你的终端AI副驾')
('https://www.cnblogs.com/vipstone/p/18950422', 'Dify发布V1.5.0：可视化故障排查！超实用')
('https://www.cnblogs.com/sun-10387834/p/18950201', '【设计模式】单例模式')
('https://www.cnblogs.com/yunshengbuzhichu/p/18950205', '《代码随想录》回溯问题学习总结')
('https://www.cnblogs.com/laoqing/p/18950075', '《数据资产管理核心技术与应用》读书笔记- 第六章-数据监控与告警（一）')
('https://www.cnblogs.com/hubert-style/p/18950083', 'React Hooks 钩子特性')
('https://www.cnblogs.com/Can-daydayup/p/18947010', '一个基于 .NET 8 开源免费、高性能、低占用的博客系统')
('https://www.cnblogs.com/shanyou/p/18946949', '微软开源 Azure Functions MCP Extension')
('https://www.cnblogs.com/skyell/p/18946936', '5分钟搞定！最全Dokploy部署n8n教程')
('https://www.cnblogs.com/zhaloe/p/18946862', 'ArkUI-X通过Stage模型开发Android端应用指南(一)')
('https://www.cnblogs.com/xiaoxi666/p/18946775', '使用Lean4进行形式化建模（以Java线程池为例）')
('https://www.cnblogs.com/johnnyzen/p/18934606', '[车载以太网] SOME/IP 参数和数据结构的序列化')
('https://www.cnblogs.com/xwwin/p/18946754', '支付宝小程序IDE版本迭代异常')
('https://www.cnblogs.com/damaoa/p/18946547', '接口设计的原则：构建优雅API的完整指南')
('https://www.cnblogs.com/www-htz-pw/p/18946385/gu-zhang-chu-li2fen-zhong-chu-lioracle-rac-zhongoc', '故障处理：2分钟处理Oracle RAC中OCR磁盘组丢失磁盘的故障')
('https://www.cnblogs.com/qq21497936/p/18946197', 'Qt+OPC开发笔记（三）：OPC客户端订阅特点消息的Demo')
('https://www.cnblogs.com/dalgleish/p/18946148', '03 - LayoutPanels例子 - TextBox')
('https://www.cnblogs.com/ThinkerQAQ/p/18945899', 'Java源码分析系列笔记-6.ReentrantLock')
('https://www.cnblogs.com/yhup/p/18945895', '一个字符串替换引发的性能血案：正则回溯与救赎之路')
('https://www.cnblogs.com/chenyishi/p/18945790', 'AI大模型应用开发入门-LangChain实现文档总结')
('https://www.cnblogs.com/zjutlitao/p/18945784', '[深度学习] 超长文，一篇讲完 NVIDIA Jetson Hello AI World 全部教程（推理 & 训练）')
('https://www.cnblogs.com/youlanjihua/p/18945723', '在仓颉开发语言中使用数据库')
('https://www.cnblogs.com/12lisu/p/18945701', '100万QPS短链系统如何设计？')
('https://www.cnblogs.com/powertoolsteam/p/18945680', '使用 .NET Core+GcExcel，生成 Excel 文件')
('https://www.cnblogs.com/linx/p/18945622', 'Web前端入门第 69 问：JavaScript Promise 提供的方法都使用过吗？')
('https://www.cnblogs.com/mjunz/p/18945612', '商品中心—12.商品溯源系统的技术文档')
('https://www.cnblogs.com/seven97-top/p/18942286', 'Java中什么是类加载？类加载的过程？')
('https://www.cnblogs.com/yychuyu/p/18945275', 'STM32学会要花费多长时间？一个从机械转行老程序员的血泪史')
('https://www.cnblogs.com/mjunz/p/18945187', '商品中心—11.商品B端搜索系统的实现文档')
('https://www.cnblogs.com/zhaloe/p/18945155', 'ArkUI-X案例解析')
('https://www.cnblogs.com/san-mu/p/18944874', '论文阅读-MD-ML: Super Fast Privacy-Preserving Machine Learning for Malicious Security with a Dishonest Majority')
('https://www.cnblogs.com/Can-daydayup/p/18945010', 'C#/.NET/.NET Core技术前沿周刊 | 第 43 期（2025年6.16-6.22）')
('https://www.cnblogs.com/ThinkerQAQ/p/18944217', 'Java源码分析系列笔记-5.AQS')
('https://www.cnblogs.com/aslanvon/p/18944922', 'STL：迭代器与常用算法')
('https://www.cnblogs.com/dayue-bc/p/18944879', 'Spring AOP 面向切面编程深度解析')
('https://www.cnblogs.com/eanojiang/p/18943588', 'C#学习日志')
('https://www.cnblogs.com/cmt/p/18944674', '上周热点回顾（6.16-6.22）')
('https://www.cnblogs.com/HarmonyOS5/p/18944536', '京东正式开源Taro on HarmonyOS C-API 版本，为鸿蒙应用跨端开发提供高性能框架')
('https://www.cnblogs.com/noear/p/18944614', 'Solon Expression Language (SnEL)：轻量高效的Java表达式引擎')
('https://www.cnblogs.com/vipstone/p/18944567', '超实用！SpringAI提示词的4种神级用法')
('https://www.cnblogs.com/youlanjihua/p/18944508', '详解HarmonyOS NEXT仓颉开发语言中的全局弹窗')
('https://www.cnblogs.com/sunstrikes/p/18944340', 'veRL代码阅读-2.Ray')
('https://www.cnblogs.com/sheng_chao/p/18944144', '浅谈开源在线客服系统与 APP 集成的技术方案与优劣势')
('https://www.cnblogs.com/fanzhidongyzby/p/18944119/chat2graph', 'Graph ⋈ Agent：Chat2Graph 如何重构 GraphRAG 范式？')
('https://www.cnblogs.com/powertoolsteam/p/18944095', '基于 Spread，在 Blazor 框架中导入 / 导出 Excel')
('https://www.cnblogs.com/jiaozai891/p/18944014', '.NET入行第4年(第二集)')
('https://www.cnblogs.com/ThinkerQAQ/p/18943971', 'Java源码分析系列笔记-4.CAS')
('https://www.cnblogs.com/huangxincheng/p/18943834', 'DotTrace系列：3. 时间度量之墙钟时间和线程时间')
('https://www.cnblogs.com/linx/p/18943769', 'Web前端入门第 68 问：JavaScript 事件循环机制中的微任务与宏任务')
('https://www.cnblogs.com/dennyLee2025/p/18943368', 'Java集合--从本质出发理解HashMap')
('https://www.cnblogs.com/1312mn/p/18943708', '.NET 8 gRPC 实现高效100G大文件断点续传工具')
('https://www.cnblogs.com/chenyishi/p/18942679', 'pytorch入门 - 基于AlexNet神经网络实现猫狗大战')
('https://www.cnblogs.com/anding/p/18939444', 'LinqPad：C#代码测试学习一品神器')
('https://www.cnblogs.com/jyzhao/p/18943673/apex-shi-zhan-di4pian-ru-he-baapex-cheng-xu-bian-c', 'APEX实战第4篇：如何把APEX程序变成“移动端APP”？')
('https://www.cnblogs.com/zsxblog/p/18943522', '在elementui,antDesign,antDesignVue中表格嵌套多个表单项校验')
('https://www.cnblogs.com/mjunz/p/18943503', '商品中心—10.商品B端搜索系统的说明文档')
('https://www.cnblogs.com/many-bucket/p/18943449', 'BIO, NIO, AIO 大白话 - 澄澈大学生也能搞懂')
('https://www.cnblogs.com/dayue-bc/p/18943306', 'Spring IoC容器与依赖注入深度解析')
('https://www.cnblogs.com/www-htz-pw/p/18943251/oracle-gu-zhang-chu-li-fen-xioracle-shu-ju-ku-li-q', 'Oracle故障处理：分析Oracle数据库离奇的多快读慢')
('https://www.cnblogs.com/Can-daydayup/p/18943217', '基于 C# 编写的轻量级工控网关和 SCADA 组态软件')
('https://www.cnblogs.com/ThinkerQAQ/p/18943121', 'Java源码分析系列笔记-3.volatile')
('https://www.cnblogs.com/zhaoylhm/p/18943067', '鸿蒙云函数与云数据库开发实践')
('https://www.cnblogs.com/yeyunfei/p/18930944', '如何基于three.js(webgl)引擎架构，实现3D机房园区，数据中心消防系统')
('https://www.cnblogs.com/zengzuo613/p/18931527', 'MCP 通信消息格式之 JSON-RPC 2.0 协议')
('https://www.cnblogs.com/wenbochang/p/18943016', '一个自认为理想主义者的程序员，写了5年公众号、博客的初衷')
('https://www.cnblogs.com/huangSir-devops/p/18940060', 'ElasticSearch的映射与文档')
('https://www.cnblogs.com/huangxincheng/p/18942584', 'DotTrace系列：2. 理解四大经典的诊断类型（下）')
('https://www.cnblogs.com/aqi00/p/18939837', 'FFmpeg开发笔记（六十八）Windows给FFmpeg集成AV1解码器libdav1d')
('https://www.cnblogs.com/dengjiahai/p/18942360', '基于.net6的一款开源的低代码、权限、工作流、动态接口平台')
('https://www.cnblogs.com/shanyou/p/18942263', 'Model Context Protocol C# SDK v0.3.0-preview.1 版本发布')
('https://www.cnblogs.com/wangerdan115/p/18942240', '鸿蒙运动项目开发：项目运行环境切换器')
('https://www.cnblogs.com/xingrenh/p/18942223', 'Claude Code 初体验 - Windows')
('https://www.cnblogs.com/leadingcode/p/18942218', '我的 Vibe Coding 的第一个项目')
('https://www.cnblogs.com/ThinkerQAQ/p/18942213', 'Java源码分析系列笔记-2.Synchronized')
('https://www.cnblogs.com/aspnetx/p/18940820', 'ETL vs ELT')
('https://www.cnblogs.com/ydswin/p/18940732', '为什么 `kubectl patch` 关闭探针不重启 Pod，重新开启却重启？')
('https://www.cnblogs.com/dayue-bc/p/18940705', '线程安全集合选择深度解析')
('https://www.cnblogs.com/ichochy/p/18940614', '[开源自荐]AI Chat 智能聊天工具，支持DeepSeek 、Gemini、Grok、OpenAI和自定义AI')
('https://www.cnblogs.com/bitzhuwei/p/18889282/openGL-implementation-in-pure-CSharp', '纯C#软实现openGL(V0.1)，黑盒变白盒')
('https://www.cnblogs.com/piwind/p/18940332', '使用acme.sh颁发TLS证书并安装到nginx/apache实现网站https访问')
('https://www.cnblogs.com/ssrheart/p/18940274', 'taskiq异步分布式任务管理器 适用fastapi')
('https://www.cnblogs.com/Big-Yellow/p/18940170', '深入浅出了解生成模型-3：Diffusion模型原理以及代码')
('https://www.cnblogs.com/ACaiGarden/p/18940020', '20250620 - Bonding 攻击事件: 项目方不创建的池子由我攻击者来创建')
('https://www.cnblogs.com/aqi00/p/18939835', 'FFmpeg开发笔记（六十七）Windows给FFmpeg集成支持RIST协议的librist')
('https://www.cnblogs.com/12lisu/p/18939721', '这5种规则引擎，真香！')
('https://www.cnblogs.com/shenchuanchao/p/18939675', '【好用推荐】免费在线图片压缩工具,附源码')
('https://www.cnblogs.com/dayue-bc/p/18939456', 'Java集合框架性能特征与使用场景深度解析')
('https://www.cnblogs.com/xiaoxiongcanguan/p/18939320', '从零开始实现简易版Netty(一) MyNetty Reactor模式')
('https://www.cnblogs.com/hunterxiong/p/18939332', 'Golang基础笔记二之字符串及其操作')
('https://www.cnblogs.com/sunstrikes/p/18939060', 'veRL代码阅读-1.论文原理')
('https://www.cnblogs.com/zhaoweiwei/p/18936701/secp256k1_1', 'secp256k1算法详解一（简介及库现在编译）')
('https://www.cnblogs.com/qiuzhendezhen/p/18938939', 'Rtpengine 全面指南 (mr13.1.1.6)：启动参数、配置详解及双网卡SBC部署实例')
('https://www.cnblogs.com/guoxiaoyu/p/18938912', '【附源码】用Spring AI通杀所有MCP客户端，简直离谱！')
('https://www.cnblogs.com/damaoa/p/18938878', 'ThreadLocal详解：线程私有变量的正确使用姿势')
('https://www.cnblogs.com/aisong/p/18938794', 'Go + WebSocket + Chrome Extension：基于真实浏览器环境的 cf_clearance 自动化获取方案')
('https://www.cnblogs.com/yhup/p/18938652', '【大数据高并发核心场景实战】 - 数据持久化之冷热分离')
('https://www.cnblogs.com/Java-Starter/p/18938640', '在本地调试 GitLab CI Pipeline (WSL搭建GitLab Runner)')
('https://www.cnblogs.com/dechinphy/p/18936707/tensor-scatter-add', 'tensor_scatter_add算子异同点')
('https://www.cnblogs.com/mjunz/p/18938589', '商品中心—9.商品卖家系统的技术文档')
('https://www.cnblogs.com/leisure_chn/p/18937567', 'USB Gadget设备软插拔异常的处理方法')
('https://www.cnblogs.com/OBCE666/p/18938345', '大模型VS小模型：论国产数据库运维AI Agent的正确打开方式')
('https://www.cnblogs.com/dalgleish/p/18938264', '03 - LayoutPanels例子 - SimpleInkCanvas')
('https://www.cnblogs.com/huangxincheng/p/18938244', 'DotTrace系列：1. 理解四大经典的诊断类型（上）')
('https://www.cnblogs.com/powertoolsteam/p/18938214', '前端技术栈加持：用 SpreadJS 实现分权限管理')
('https://www.cnblogs.com/fanliang11/p/18938169', 'dotnetty 新的篇章- 开源')
('https://www.cnblogs.com/zhaloe/p/18937271', 'ArkUI-X平台桥接Bridge说明')
('https://www.cnblogs.com/carpell/p/18938143', '【语义分割专栏】4：deeplab系列原理篇')
('https://www.cnblogs.com/lymblog/p/18938072', 'AEM 与 ActiveMQ 集成方案详解')
('https://www.cnblogs.com/experdot/p/18938018', '3种方法让大语言模型提取任意文档中的知识点')
('https://www.cnblogs.com/aslanvon/p/18938024', 'c++ 预处理 编译 链接 文件组织形式')
('https://www.cnblogs.com/linx/p/18937897', 'Web前端入门第 67 问：JavaScript 中的面向对象编程')
('https://www.cnblogs.com/fs7744/p/18937649', 'VKProxy新增速率限制功能')
('https://www.cnblogs.com/wangerdan115/p/18937605', '鸿蒙运动项目开发：封装超级好用的 RCP 网络库（下）—— 实战应用')
('https://www.cnblogs.com/leisure_chn/p/18937564', 'USB Gadget设备枚举失败的处理方法')
('https://www.cnblogs.com/wubayue/p/18937518', '设计模式：简单工厂、工厂方法与抽象工厂')
('https://www.cnblogs.com/xiaoxi666/p/18935987', '用好 JUnit 5 的高级特性：提升单测效率和质量')
('https://www.cnblogs.com/zhaloe/p/18937262', 'ArkUI-X应用工程结构说明')
('https://www.cnblogs.com/jyzhao/p/18937250/cong-kan-dong-shi-jie-dao-gai-zao-shi-jieai-fa-zha', '从“看懂世界”到“改造世界”：AI发展的四个阶段你了解了吗？')
('https://www.cnblogs.com/dayue-bc/p/18937103', 'Java 集合框架底层数据结构实现深度解析')
('https://www.cnblogs.com/Meth-nylon/p/18936550', '前/后向自动微分的简单推导与rust简单实现')
('https://www.cnblogs.com/damaoa/p/18936921', 'Java线程池详解：高效并发编程的核心利器')
('https://www.cnblogs.com/zhuweisky/p/18936492', 'C#实现语音预处理：降噪、静音检测、自动增益（附Demo源码）')
('https://www.cnblogs.com/deali/p/18936652', 'PVE折腾笔记 (3) 在原QNAP使用的硬盘上创建ZFS')
('https://www.cnblogs.com/xwwin/p/18936600', 'uni-app项目process is not defined')
('https://www.cnblogs.com/springsource/p/18936461', 'Java团队Cursor最佳实践：3分钟构建「零泄漏」AI开发环境')
('https://www.cnblogs.com/huangxincheng/p/18936554', '记一次 .NET 某无语的电商采集系统 CPU爆高分析')
('https://www.cnblogs.com/OBCE666/p/18936507', '物无弃材\xa0——\xa0混闪机型部署 OceanBase 集群的探索')
('https://www.cnblogs.com/lymblog/p/18936489', 'CURL 命令的用法详解')
('https://www.cnblogs.com/chegan/p/18936210', '历时半年，我将一个大型asp.net的零代码快速开发平台转成了java')
('https://www.cnblogs.com/chenyishi/p/18936359', 'AI大模型应用开发-用LangChain构建PAL应用：SQL的生成与执行')
('https://www.cnblogs.com/zhaloe/p/18935409', 'ArkUI-X跨平台技术落地-华为运动健康(二)')
('https://www.cnblogs.com/yzsunlei/p/18936264', '【前端AI实践】Lingma：使用工具辅助开发让你停不下来')
('https://www.cnblogs.com/bokong/p/18936227', '在Linux下使用wxWidgets进行跨平台GUI开发（三）')
('https://www.cnblogs.com/youlanjihua/p/18936118', '详解鸿蒙Next仓颉开发语言中的全屏模式')
('https://www.cnblogs.com/vivotech/p/18936065', 'Spring Boot 启动优化实践')
('https://www.cnblogs.com/guoxiaoyu/p/18935990', '重要通知：spring-ai-hunyuan 已兼容 Spring AI 稳定版！')
('https://www.cnblogs.com/lucky_hu/p/18935982', 'Coze工作流实战：一键生成治愈风格视频')
('https://www.cnblogs.com/IwishIcould/p/18935216', 'Flex布局,绝对定位,层叠布局Stack的详细讲解')
('https://www.cnblogs.com/seven97-top/p/18928712', 'JVM内存结构33连问')
('https://www.cnblogs.com/mjunz/p/18935677', '商品中心—8.商品C端处理高并发的技术文档')
('https://www.cnblogs.com/jinjiangongzuoshi/p/18935632', '推荐五大AI+MCP自动化测试工具！')
('https://www.cnblogs.com/XY-Heruo/p/18935503', '[python]动态实例化')
('https://www.cnblogs.com/hunterxiong/p/18935485', 'Golang基础笔记一之变量声明和类型介绍')
('https://www.cnblogs.com/zhaloe/p/18935406', 'ArkUI-X跨平台技术落地-华为运动健康(一)')
('https://www.cnblogs.com/byhgz/p/18935251', 'js代码修改react框架的input的值-2025年6月')
('https://www.cnblogs.com/deali/p/18935219', '个人数据保全计划：如何安全地备份数据')
('https://www.cnblogs.com/xibaoyu/p/18935198', 'Seo工具使用与流量数据观察实践（中）')
('https://www.cnblogs.com/jvxiao/p/18935168', '搭建个人博客系列--(4) 利用Github Actions自动构建博客')
('https://www.cnblogs.com/lvpp13/p/18934961', "非常'肤浅'的理解MVVM")
('https://www.cnblogs.com/du-hong/p/18420303', '《刚刚问世》系列初窥篇-Java+Playwright自动化测试-18- 操作模态对话框弹窗 （详细教程）')
('https://www.cnblogs.com/youlanjihua/p/18934700', '鸿蒙Next仓颉语言开发实战教程：懒加载')
('https://www.cnblogs.com/dayue-bc/p/18934617', 'Java 并发工具类核心使用场景深度解析')
('https://www.cnblogs.com/KubeExplorer/p/18934587', 'K8s v1.31 新特性：ImageVolume，允许将镜像作为 Volume 进行挂载')
('https://www.cnblogs.com/lesliexin/p/18923105', '[原创]《C#高级GDI+实战：从零开发一个流程图》第03章：画一个线，连接两个矩形！')
('https://www.cnblogs.com/echolun/p/18934437', 'Review-Gate MCP，让你的 cursor request 次数翻 5 倍')
('https://www.cnblogs.com/goodcitizen/p/18889661/cpp20_coroutine_is_equal_to_function_plus_state_machine', '协程本质是函数加状态机——零基础深入浅出 C++20 协程')
('https://www.cnblogs.com/powertoolsteam/p/18934279', 'C# 将 CSV 转化为 Excel')
('https://www.cnblogs.com/somefuture/p/18933713', 'git-intelligence-message 1.3.2  发布了，智能生成、提交git的工具')
('https://www.cnblogs.com/leisure_chn/p/18934211', 'USBIP技术简介')
('https://www.cnblogs.com/ayuday/p/18934189', 'vitepress静态站点支持离线浏览器本地全文搜索功能')
('https://www.cnblogs.com/wangerdan115/p/18934180', '鸿蒙运动项目开发：封装超级好用的 RCP 网络库（中）—— 错误处理，会话管理与网络状态检测篇')
('https://www.cnblogs.com/abinzhao/p/18933800', 'HarmonyOS桌面快捷功能开发指南：从原理到实战')
('https://www.cnblogs.com/cuipengfei/p/18933709', '如何给 GitHub Copilot "洗脑”，让 AI 精准遵循指令产出高质量代码')
('https://www.cnblogs.com/zhaloe/p/18931888', 'ArkUI-X跨平台应用改造指南')
('https://www.cnblogs.com/thisiswhy/p/18933669', '编码之道，道心破碎。')
('https://www.cnblogs.com/johnnyzen/p/18931813', '[计算机组成原理/Java] 字符集编码: Unicode 字符集(UTF8/UTF16/UTF32) / `BOM`(Byte Order Mark/字节序标记) / UnicodeTextUtils')
('https://www.cnblogs.com/kuangdaoyizhimei/p/18933359', 'SpringBoot集成TOTP双因素认证（2FA）实战')
('https://www.cnblogs.com/HarmonyOSSDK/p/18933348', '如何在应用中实现地图关键字搜索和标记聚合功能？')
('https://www.cnblogs.com/fffffff/p/18933341', 'vue3 + springboot实现微信登录')
('https://www.cnblogs.com/root429/p/18933245', 'FactoryBean 和BeanFactory的傻傻的总是分不清？')
('https://www.cnblogs.com/lymblog/p/18933193', 'AEM6.5集成Redis详细步骤(附代码)')
('https://www.cnblogs.com/sanmulx/p/18933186', 'Xshell 详细安装与配置教程：从下载到高效使用')
('https://www.cnblogs.com/mjunz/p/18933087', '商品中心—7.自研缓存框架的技术文档')
('https://www.cnblogs.com/12lisu/p/18932998', '千万级大表，如何做性能调优？')
('https://www.cnblogs.com/chenyishi/p/18932975', 'AI大模型应用开发-用LangChain构建带Agen流程的RAG系统')
('https://www.cnblogs.com/huangxincheng/p/18932886', '记一次 .NET 某发证机系统 崩溃分析')
('https://www.cnblogs.com/youlanjihua/p/18932841', '鸿蒙Next仓颉语言开发实战教程：聊天页面')
('https://www.cnblogs.com/dayue-bc/p/18932795', '线程安全与锁机制深度解析')
('https://www.cnblogs.com/damaoa/p/18932748', 'Java并发利器：CountDownLatch深度解析与实战应用')
('https://www.cnblogs.com/yupi/p/18932624', '不写代码，让 AI 生成手机 APP！保姆级教程')
('https://www.cnblogs.com/linx/p/18932449', 'Web前端入门第 66 问：JavaScript 作用域应用场景（闭包）')
('https://www.cnblogs.com/sun-10387834/p/18932382', '【深入理解Spring AOP】核心原理与代理机制详解')
('https://www.cnblogs.com/powertoolsteam/p/18932383', 'C# 异步编程：从 async/await 到 Task 并行库的全面解析')
('https://www.cnblogs.com/sapSB/p/18932282', '用配置表给WEB DYNPRO做配置报表（限十个查询条件）')
('https://www.cnblogs.com/wang_yb/p/18932272', 'Just：告别 Makefile 的现代命令行任务运行器')
('https://www.cnblogs.com/wangerdan115/p/18932269', '鸿蒙运动项目开发：封装超级好用的 RCP 网络库（上）—— 请求参数封装，类型转化器与日志记录篇')
('https://www.cnblogs.com/xueweihan/p/18932140', '一周 Star 破万的开源项目「GitHub 热点速览」')
('https://www.cnblogs.com/shanyou/p/18932238', '.NET 10 引入 后量子密码学 （PQC）')
('https://www.cnblogs.com/Can-daydayup/p/18929377', 'Visual Studio 2022 中的 EF Core 反向工程和模型可视化扩展插件')
('https://www.cnblogs.com/mjunz/p/18931935', '商品中心—6.商品考核系统的技术文档')
('https://www.cnblogs.com/charlee44/p/18931929', '给Markdown渲染网页增加一个目录组件(Vite+Vditor+Handlebars)(下)')
('https://www.cnblogs.com/freephp/p/18931921', 'AWS学习笔记之Lambda执行权限引发的思考')
('https://www.cnblogs.com/graphics/p/18931917', 'TypeScript实用类型之Omit')
('https://www.cnblogs.com/dayue-bc/p/18931894', 'Java 内存模型与 Happens-Before 关系深度解析')
('https://www.cnblogs.com/wuty/p/18931865', '记录.Net 8 发布增加 PublishTrimmed 裁剪选项，调用WMI 的ManagementObject 异常')
('https://www.cnblogs.com/bigtwetwet/p/18931842', '三种AI人机交互系统的记忆模块对比：小智、OPEN-LLM-VTUBER和MaiBot')
('https://www.cnblogs.com/Can-daydayup/p/18931786', 'C#/.NET/.NET Core技术前沿周刊 | 第 42 期（2025年6.9-6.15）')
('https://www.cnblogs.com/longronglang/p/18931762', '如何分辨大模型的好坏？')
('https://www.cnblogs.com/yby6/p/18931607', '继ChatGPT的热潮AI的新产物-智能体元器Agent平台')
('https://www.cnblogs.com/zhaoweiwei/p/18919130/cuda_vs2019', 'VS2019+CUDA12.5入门')
('https://www.cnblogs.com/aslanvon/p/18931148', 'c++ 函数 类')
('https://www.cnblogs.com/zhiranok/p/18931086/arkbinder', '鸿蒙 Native API 的封装库 h2lib_arkbinder')
('https://www.cnblogs.com/OBCE666/p/18931058', '世事洞明皆学问 — 如何理解 OB 4.x 版本中的日志流？')
('https://www.cnblogs.com/huangxincheng/p/18931014', '记一次 .NET 某SaaS版CRM系统 崩溃分析')
('https://www.cnblogs.com/zhaloe/p/18930328', 'ArkUI-X在Android平台动态化开发指南')
('https://www.cnblogs.com/powertoolsteam/p/18930958', '基于 C# 和 .NET 的 Spread.NET 数据处理实战')
('https://www.cnblogs.com/lesliexin/p/18930533', '（原创）[C#]一步步解决DotNetZip因超长路径（MAX_PATH）报错的问题。')
('https://www.cnblogs.com/leisure_chn/p/18930767', 'USB工程应用基础概念简介')
('https://www.cnblogs.com/cmt/p/18930751', '上周热点回顾（6.9-6.15）')
('https://www.cnblogs.com/12lisu/p/18930715', 'Spring用到的10种设计模式，真巧妙！')
('https://www.cnblogs.com/dennyLee2025/p/18930667', 'Java集合--LinkedList源码可视化')
('https://www.cnblogs.com/springsource/p/18930652', '再不用手写Commit！AI自动总结代码变更，Git提交效率')
('https://www.cnblogs.com/damaoa/p/18930567', 'MySQL事务：工作原理与实用指南')
('https://www.cnblogs.com/seven97-top/p/18928700', '你了解CAS吗？有什么问题吗？如何解决？')
('https://www.cnblogs.com/wJiang/p/18930549', '深入剖析开源AI阅读器项目Saga Reader基于大模型的文本转换与富文本渲染优化方案')
('https://www.cnblogs.com/Can-daydayup/p/18927721', '推荐 3 种 .NET Windows 桌面应用程序自动更新解决方案')
('https://www.cnblogs.com/shanyou/p/18930500', '.NET 10 支持Linux/Unix 的Shebang（Hashbang）')
('https://www.cnblogs.com/zhaloe/p/18930325', 'ArkUI-X框架LogInterface使用指南')
('https://www.cnblogs.com/fkxxgis/p/18929933', 'ArcGIS工具操作报错999999的通用处理方式')
('https://www.cnblogs.com/pam-sh/p/18929904', '基于混合检索重排序策略的大模型增强方法')
('https://www.cnblogs.com/NickYao/p/18929857', '告别脆弱的 Playwright 测试：为什么基于 YAML 的测试是未来趋势')
('https://www.cnblogs.com/deali/p/18929821', 'PVE折腾笔记 (2) 挂载之前在QNAP里使用的硬盘')
('https://www.cnblogs.com/zsxblog/p/18929807', '改造jsp项目的alert框和confirm框')
('https://www.cnblogs.com/samhou/p/18929792/coding-with-grandma-flexbox', '手把手 Flexbox——从零开始的奶奶级 Web 开发教程3')
('https://www.cnblogs.com/wang_yb/p/18929737', '掌握ChangeSpeed类：让数学动画速度随心而动！')
('https://www.cnblogs.com/koushenhai/p/18929660', 'Spring Cloud Gateway实现分布式限流和熔断降级')
('https://www.cnblogs.com/huangqingshi/p/18929621', '基于Spring AI服务，开发MCP服务')
('https://www.cnblogs.com/youlanjihua/p/18929598', '鸿蒙Next仓颉语言开发实战教程：设置页面')
('https://www.cnblogs.com/qife122/p/18929553', 'AgenticSeek - 完全本地的AI助手替代方案')
('https://www.cnblogs.com/cmt/p/18929466', '完成微博外链备案，微博中直接可以打开园子的链接')
('https://www.cnblogs.com/OBCE666/p/18929418', 'DBA 必知必会\xa0——\xa0OB 4.x 版本如何查询磁盘空间占用情况？')
('https://www.cnblogs.com/Can-daydayup/p/18929376', '一个基于 .NET 开源、模块化 AI 图像生成 Web 用户界面')
('https://www.cnblogs.com/huangSir-devops/p/18929338', 'LogStash介绍及二进制安装')
('https://www.cnblogs.com/aqi00/p/18928403', 'FFmpeg开发笔记（六十六）Windows给FFmpeg集成LC3音频的编码器liblc3')
('https://www.cnblogs.com/wangerdan115/p/18929284', '鸿蒙运动开发实战：打造专属运动视频播放器')
('https://www.cnblogs.com/chenyishi/p/18929193', 'AI大模型应用开发入门-LangChain开发RAG增强检索生成')
('https://www.cnblogs.com/dayue-bc/p/18929192', 'JIT 编译优化原理深度解析')
('https://www.cnblogs.com/dragonet-Z/p/18928900', 'C#实现欧姆龙 HostLink 通讯协议库')
('https://www.cnblogs.com/lucky_hu/p/18928909', 'Coze工作流实战：一键生成像素风格视频')
('https://www.cnblogs.com/huangSir-devops/p/18928862', 'Ubuntu二进制安装ElasticSearch7.17.x版本集群')
('https://www.cnblogs.com/SkyXZ/p/18928783', '无法直连 SSH？一招反向SSH搞定内网到公网的远程连接问题')
('https://www.cnblogs.com/lanfengqi/p/18928766', '使用apache amoro + trino+minio搭建iceberg数据湖架构')
('https://www.cnblogs.com/zhangpengfei5945/p/18928741', '排查问题实践')
('https://www.cnblogs.com/wJiang/p/18928723', '【实战】基于 Tauri 和 Rust 实现基于无头浏览器的高可用网页抓取')
('https://www.cnblogs.com/xwwin/p/18928708', 'uni-app项目loading显示方案')
('https://www.cnblogs.com/shenchuanchao/p/18928583', 'C# WinForms 实现打印监听组件')
('https://www.cnblogs.com/greyzeng/p/18928342', '《深度学习：基础与概念》第一章 学习笔记与思考')
('https://www.cnblogs.com/maomao777/p/18928468', '有关Spring事务的传播机制')
('https://www.cnblogs.com/bokong/p/18928436', '使用wxWidgets进行跨平台GUI开发（附1）')
('https://www.cnblogs.com/bricheersz/p/18928417', '你应该懂的AI大模型（六）之 transformers')
('https://www.cnblogs.com/aqi00/p/18928398', 'FFmpeg开发笔记（六十五）Linux给FFmpeg集成LC3音频的编码器liblc3')
('https://www.cnblogs.com/fs7744/p/18928323', 'VKProxy已提供命令行工具，镜像和简单的ui')
('https://www.cnblogs.com/huangSir-devops/p/18928261', 'GitLab介绍及Docker部署GitLab')
('https://www.cnblogs.com/aslanvon/p/18928233', '智能指针')
('https://www.cnblogs.com/zxlh1529/p/18928151', '微信小程序实现用户进行推客的注册绑定')
('https://www.cnblogs.com/www-htz-pw/p/18928119/te-shu-hui-fu-zui-jian-dan-debbed-xiu-gaiasm-de-sh', '特殊恢复：最简单的BBED修改ASM的数据块的方法')
('https://www.cnblogs.com/dayue-bc/p/18928082', 'JVM 类加载过程与字节码执行深度解析')
('https://www.cnblogs.com/ask-tang/p/18928037', '[GESP样题 七级] 最长不下降子序列题解')
('https://www.cnblogs.com/chenyishi/p/18928020', 'pytorch入门 - VGG16神经网络')
('https://www.cnblogs.com/noear/p/18927997', 'Solon AI + MCP实战：5行代码搞定天气查询，LLM从此告别数据孤岛')
('https://www.cnblogs.com/SkyXZ/p/18927874', '手把手教你实现PyTorch版ViT：图像分类任务中的Transformer实战')
('https://www.cnblogs.com/deali/p/18927750', 'PVE折腾笔记 (1) 告别卡顿的QTS，在威联通TS464c上安装PVE系统')
('https://www.cnblogs.com/Can-daydayup/p/18927715', '一个基于 .NET 8 + Ant Design Blazor 开发的简洁现代后台管理框架')
('https://www.cnblogs.com/jiujuan/p/18927672', '微服务架构学习与思考(16)：SOA架构与微服务架构对比分析？它们之间区别是什么?')
('https://www.cnblogs.com/xiao987334176/p/18927305', 'Easy-Dataset实现文档生成数据集')
('https://www.cnblogs.com/OBCE666/p/18927518', '网易游戏DB SaaS引入OceanBase：存储成本降60%，备份恢复提速3倍')
('https://www.cnblogs.com/facingscreen/p/18927460', 'Cocos3内置Effect（着色器）介绍')
('https://www.cnblogs.com/root429/p/18927414', 'Java 锁升级机制详解')
('https://www.cnblogs.com/blbl-blog/p/18927334', 'UniApp前端+Java后端技术栈 解析微信支付功能的设计实现与关键实践')
('https://www.cnblogs.com/youlanjihua/p/18927115', '鸿蒙Next仓颉语言开发实战教程：订单详情')
('https://www.cnblogs.com/chenyishi/p/18926783', 'AI大模型应用开发入门-LangChain开发Agent')
('https://www.cnblogs.com/BaiCai/p/18926606', 'AppBox拖拽设计增删改查用户界面')
('https://www.cnblogs.com/deali/p/18926529', '提升Avalonia UI质感，跨平台图标库选型实践')
('https://www.cnblogs.com/powertoolsteam/p/18926505', 'C# 中委托和事件的深度剖析与应用场景')
('https://www.cnblogs.com/linx/p/18926463', 'Web前端入门第 65 问：JavaScript 函数参数各种使用方式')
('https://www.cnblogs.com/wangerdan115/p/18926451', 'HarmonyOS运动开发：深度解析文件预览的正确姿势')
('https://www.cnblogs.com/IwishIcould/p/18911585', 'ResizeObserver和IntersectionObserver的详细讲解')
('https://www.cnblogs.com/mjunz/p/18926438', '商品中心—5.商品消息处理系统的技术文档')
('https://www.cnblogs.com/www-htz-pw/p/18926363/gu-zhang-zhen-duanasm-mo-ming-chu-xiangc-deng-dai', '故障诊断：ASM莫名出现GC等待事件、ADG的MRP进程HANG住')
('https://www.cnblogs.com/deali/p/18926233', '告别图形界面：Windows系统OpenSSH服务部署')
('https://www.cnblogs.com/zhaloe/p/18926223', 'ArkUI-X在Android上使用Fragment开发指南')
('https://www.cnblogs.com/charlee44/p/18926136', '给Markdown渲染网页增加一个目录组件(Vite+Vditor+Handlebars)(上)')
('https://www.cnblogs.com/hnzhengfy/p/18925817/element_select_all', 'Select 组件实现【全选】（基于 Element）')
('https://www.cnblogs.com/xwwin/p/18926021', 'uni-app项目怎么实现多服务环境切换')
('https://www.cnblogs.com/vipstone/p/18925927', 'Dify实战案例：AI邮件批量发送器！')
('https://www.cnblogs.com/JuiceData/p/18925745', '深度解析 JuiceFS 权限管理：Linux 多种安全机制全兼容')
('https://www.cnblogs.com/12lisu/p/18925651', '接口被刷百万QPS，怎么防？')
('https://www.cnblogs.com/youlanjihua/p/18925620', '详解HarmonyOS NEXT系统中ArkTS和仓颉的混合开发')
('https://www.cnblogs.com/kuangdaoyizhimei/p/18925535', '万字长文彻底剖析Python正则表达式')
('https://www.cnblogs.com/Can-daydayup/p/18925322', '一个开源免费、功能丰富的 WPF 自定义控件资源库')
('https://www.cnblogs.com/Colin-Cai/p/18791060', '有限Abel群的结构(2)')
('https://www.cnblogs.com/dtux/p/18925240', 'AI 赋能编程，Coding新范式')
('https://www.cnblogs.com/DAYceng/p/18925165', '一则复杂 SQL 改写后有感')
('https://www.cnblogs.com/huangxincheng/p/18925152', '聊一聊 Linux 上对函数进行 hook 的两种方式')
('https://www.cnblogs.com/vivotech/p/18925118', 'vivo Pulsar 万亿级消息处理实践(2)-从0到1建设 Pulsar 指标监控链路')
('https://www.cnblogs.com/chenyishi/p/18925112', 'AI大模型应用开发入门-LangChain开发聊天机器人ChatBot')
('https://www.cnblogs.com/wangerdan115/p/18925095', 'HarmonyOS运动开发：打造便捷的静态快捷菜单')
('https://www.cnblogs.com/code-daily/p/18924622', 'C# 锁机制全景与高效实践：从 Monitor 到 .NET 9 全新 Lock')
('https://www.cnblogs.com/powertoolsteam/p/18924171', 'C# 与低代码平台的融合：以活字格为例的 Web API 开发实践')
('https://www.cnblogs.com/DustPolaris/p/18924818', 'Typora优化教程：如何使用回车键来实现「换行」而非「分段」（类似Obsidian）')
('https://www.cnblogs.com/jtea/p/18925026', 'tomcat为什么假死了.md')
('https://www.cnblogs.com/springsource/p/18924991', 'MCP赋能，给Cursor插上“外挂翅膀”：实战操作数据库')
('https://www.cnblogs.com/mjunz/p/18924919', '商品中心—4.商品属性与状态流转的技术文档')
('https://www.cnblogs.com/seven97-top/p/18916746', '线程池中execute和submit的区别？')
('https://www.cnblogs.com/du-hong/p/18405731', '《刚刚问世》系列初窥篇-Java+Playwright自动化测试-17-  如何优雅地切换浏览器多窗口（详细教程）')
('https://www.cnblogs.com/sheng-jie/p/18924852', '\u200b微软 AI Agent三剑客：AutoGen、Semantic Kernel与MEAI的协同演进')
('https://www.cnblogs.com/experdot/p/18924253', 'AI 聊天应用的 10 条高级需求')
('https://www.cnblogs.com/jyzhao/p/18924802/ceng-jing-feng-guang-wu-xian-de-oracle-dba-yi-jing', '曾经风光无限的 Oracle DBA 已经落伍了吗？')
('https://www.cnblogs.com/ludangxin/p/18923413', '3. LangChain4j-RAG，实现简单的text-sql功能')
('https://www.cnblogs.com/XY-Heruo/p/18924647', '[python]requests VS httpx VS aiohttp')
('https://www.cnblogs.com/mjunz/p/18924638', '商品中心—3.商品可采可补可售的技术文档')
('https://www.cnblogs.com/hubuguilai/p/18924504', '压缩感知的感性认识')
('https://www.cnblogs.com/zhaloe/p/18922900', 'ArkUI-X平台差异化')
('https://www.cnblogs.com/zackstang/p/18924372', '构建基于Serverless架构的向量检索MCP Server')
('https://www.cnblogs.com/bokong/p/18924367', '在Linux下使用wxWidgets进行跨平台GUI开发（二）')
('https://www.cnblogs.com/yexiaochai/p/18924244', '2025AI元年，常见智能体盘点')
('https://www.cnblogs.com/lsy131479/p/18923991', '《程序员的底层思维》读后感')
('https://www.cnblogs.com/youlanjihua/p/18923853', '鸿蒙Next仓颉语言开发实战教程：订单列表')
('https://www.cnblogs.com/zhaloe/p/18922898', 'ArkUI-x跨平台Bridge最佳实践')
('https://www.cnblogs.com/softlee/p/18915269', '基于谷歌内核（Chromium）浏览器Cookie值的读取工具')
('https://www.cnblogs.com/liwen01/p/18922662', '蓝牙基础(三)：蓝牙信道、跳频与选择算法')
('https://www.cnblogs.com/powertoolsteam/p/18923562', '基于语义检索的知识问答（RAG范式）')
('https://www.cnblogs.com/wofeiliangren/p/18923487', 'WinForm中实现Adobe PDF Reader实现旋转PDF功能')
('https://www.cnblogs.com/HarmonyOSSDK/p/18923471', '多格式文件在线预览，提升移动设备文件处理效率与体验')
('https://www.cnblogs.com/xiangji/p/18923431', '.net 实现 CQRS 的一个设想')
('https://www.cnblogs.com/edisonfish/p/18923418', '运维排查 | SaltStack 远程命令执行中文乱码问题')
('https://www.cnblogs.com/sun-10387834/p/18923401', '【分布式事务】从基础概念到现代解决方案的全面解析')
('https://www.cnblogs.com/wang_yb/p/18923254', '规则学习：让机器学习像人类一样思考的可解释之路')
('https://www.cnblogs.com/wangerdan115/p/18923218', 'HarmonyOS运动开发：如何选择并上传运动记录')
('https://www.cnblogs.com/chenyishi/p/18923187', 'pytorch入门 - AlexNet神经网络')
('https://www.cnblogs.com/JavaBuild/p/18923102', '时隔半年，拾笔分享：来自一个大龄程序员的迷茫自问')
('https://www.cnblogs.com/Can-daydayup/p/18922861', '不写一行代码 .NET 使用 FluentCMS 快速构建现代化内容管理系统（CMS）')
('https://www.cnblogs.com/shanyou/p/18923085', 'WineHQ 发布的  Framework Mono 6.14 的这个特性对Windows Forms 用户来说肯定很感兴趣')
('https://www.cnblogs.com/qizhou/p/18916588', 'Benchmark论文解读：Evaluating the Ripple Effects of Knowledge Editing in Language Models')
('https://www.cnblogs.com/mjunz/p/18923014', '商品中心—2.商品生命周期和状态的技术文档')
('https://www.cnblogs.com/zhaloe/p/18919770', 'ArkUI-X与Android桥接通信之方法回调')
('https://www.cnblogs.com/wngtk/p/18922889', '现代 Python 包管理器 uv')
('https://www.cnblogs.com/Can-daydayup/p/18922869', 'C#/.NET/.NET Core技术前沿周刊 | 第 41 期（2025年6.1-6.8）')
('https://www.cnblogs.com/kqdssheng/p/18922628', 'AD 横向移动-LSASS 进程转储')
('https://www.cnblogs.com/beluga/p/18720205', 'Git 操作进阶')
('https://www.cnblogs.com/yexiaochai/p/18922591', 'Coze/Dify/FastGPT/N8N ：该如何选择Agent平台？')
('https://www.cnblogs.com/johnnyzen/p/18921308', '[网络传输/序列化/CPU/内存/计算机组成原理] 字节序/大小端')
('https://www.cnblogs.com/aisong/p/18922518', '按下 F12 打开开发者工具，它凭什么能监控所有网络请求？')
('https://www.cnblogs.com/sun-10387834/p/18922477', '【Zookeeper】ZooKeeper集群搭建与选举原理终极指南（Docker版 + 三角色详解）')
('https://www.cnblogs.com/vipstone/p/18922455', 'Dify实战案例：MySQL查询助手！嘎嘎好用')
('https://www.cnblogs.com/anding/p/18919985', 'C#-Visual Studio工具使用实践')
('https://www.cnblogs.com/shenchuanchao/p/18922306', 'Hexo-butterfly 接入腾讯混元大模型自动生成文章摘要(保姆教程)')
('https://www.cnblogs.com/xiao987334176/p/18922128', 'SwanLab入门深度学习：Qwen3大模型指令微调')
('https://www.cnblogs.com/ahfuzhang/p/18922174', 'golang中写个字符串遍历谁不会？且看我如何提升 50 倍')
('https://www.cnblogs.com/youlanjihua/p/18922122', '鸿蒙Next仓颉语言开发实战教程：下拉刷新和上拉加载更多')
('https://www.cnblogs.com/zhally/p/18922066', 'Maui 实践：为控件动态扩展 DragDrop 能力')
('https://www.cnblogs.com/linx/p/18922032', 'Web前端入门第 64 问：JavaScript 几种函数定义方式有什么区别？')
('https://www.cnblogs.com/wJiang/p/18921999', 'Svelte 5 状态管理全解析：从响应式核心到项目实战')
('https://www.cnblogs.com/morec/p/18921958', '理解堆排序的本质：建堆 + 交换 + 装箱+重复')
('https://www.cnblogs.com/xwwin/p/18921909', '支付宝小程序外链跳转调试爬坑')
('https://www.cnblogs.com/mikeCao/p/18921879', '深度解析：虚拟列表性能优化的技术艺术')
('https://www.cnblogs.com/zhangmingcheng/p/18921796', 'LLM 上下文长度详细介绍')
('https://www.cnblogs.com/lesliexin/p/18919737', '[原创]《C#高级GDI+实战：从零开发一个流程图》第02章：画一个矩形，能拖动！')
('https://www.cnblogs.com/huangxincheng/p/18921837', 'MinHook 对.NET底层的 SendMessage 拦截真实案例反思')
('https://www.cnblogs.com/du-hong/p/18403898', '《刚刚问世》系列初窥篇-Java+Playwright自动化测试-16- iframe操作-监听事件和执行js脚本 （详细教程）')
('https://www.cnblogs.com/powertoolsteam/p/18921734', 'AI智能体的技术架构与解决方案')
('https://www.cnblogs.com/WindrunnerMax/p/18921725', '从零实现富文本编辑器#5-编辑器选区模型的状态结构表达')
('https://www.cnblogs.com/huangSir-devops/p/18916651', 'K8s中的RBAC认证授权之基于HTTPS证书给User授权认证')
('https://www.cnblogs.com/chenyishi/p/18921706', 'pytorch入门 - 修改huggingface大模型配置参数')
('https://www.cnblogs.com/xueweihan/p/18921524', '3 个超火的开源项目「GitHub 热点速览」')
('https://www.cnblogs.com/gogoSandy/p/18921503', '解密prompt系列55.Agent Memory的工程实现 - Mem0 & LlamaIndex')
('https://www.cnblogs.com/piwind/p/18921471', '用docker compose部署flarum论坛系统带msmtpd redis Meilisearch')
('https://www.cnblogs.com/mjunz/p/18921436', '商品中心—1.B端建品和C端缓存的技术文档')
('https://www.cnblogs.com/cpd75/p/18921361', '多按键实例讲解状态机')
('https://www.cnblogs.com/www-htz-pw/p/18921161/dba-bi-bei-shen-qi-rangoracle-guan-ku-bu-zai-xin-j', 'DBA必备神器：让Oracle关库不再心惊胆战！')
('https://www.cnblogs.com/cmt/p/18921109', '【故障公告】6月9日 17:24~17:34 再次遭遇攻击（晚上遭遇更疯狂的攻击）')
('https://www.cnblogs.com/youlanjihua/p/18921091', '鸿蒙Next仓颉语言开发实战教程：店铺详情页')
('https://www.cnblogs.com/sun-10387834/p/18920960', '【Zookeeper从入门到实战】SpringBoot整合完整指南')
('https://www.cnblogs.com/alioth01/p/18920952', 'DeepSeek + Mermaid：零代码玩转专业流程图')
('https://www.cnblogs.com/timothy020/p/18920941', 'TUF系统概述：Docker镜像安全分发的背后功臣')
('https://www.cnblogs.com/xiaobaiysf/p/18920913', '通过 MCP 服务对接 PostgreSQL 问数 （详细实操说明）')
('https://www.cnblogs.com/token-ai/p/18920899', ' 开源提示词优化神器来了！一键优化Function Calling和MCP提示词，让你的AI应用性能飞跃')
('https://www.cnblogs.com/wangerdan115/p/18920758', 'HarmonyOS运动开发：打造你的专属运动节拍器')
('https://www.cnblogs.com/johnnyzen/p/18920751', '[Java/Python] Java 基于命令行调用 Python')
('https://www.cnblogs.com/justnow/p/18920352', 'GGTalk 开源即时通讯系统源码剖析之：远程磁盘 （低调赠送GGTalk最新源码）')
('https://www.cnblogs.com/zhangmingcheng/p/18914445', 'LLM 输出配置 (LLM output configuration)')
('https://www.cnblogs.com/huangSir-devops/p/18919321', 'K8s新手系列之CronJob')
('https://www.cnblogs.com/powertoolsteam/p/18908061', 'AI智能体介绍与典型应用场景分析')
('https://www.cnblogs.com/huangxincheng/p/18920461', 'MinHook 如何对.NET底层的 Win32函数 进行拦截')
('https://www.cnblogs.com/KendoCross/p/18920456', 'AI时代Hello World详细教程之LLM微调(SFT)')
('https://www.cnblogs.com/12lisu/p/18920430', '假如给你1亿的Redis key，如何高效统计？')
('https://www.cnblogs.com/wang_yb/p/18920426', '概率图模型：机器学习的结构化概率之道')
('https://www.cnblogs.com/lesliexin/p/18907919', '[原创]《C#高级GDI+实战：从零开发一个流程图》第01章：有什么用、有什么效果？')
('https://www.cnblogs.com/cmt/p/18920326', '上周热点回顾（6.2-6.8）')
('https://www.cnblogs.com/qq21497936/p/18920324', 'GStreamer开发笔记（五）：gstreamer创建组件、管道和总线实现简单的播放器')
('https://www.cnblogs.com/Acc1oFl4g/p/18920291', '对apache服务器环境下利用.htaccess配置文件完成文件上传的理解')
('https://www.cnblogs.com/carpell/p/18920214', '【语义分割专栏】3：Segnet实战篇(附上完整可运行的代码pytorch)')
('https://www.cnblogs.com/depend/p/18920193', '推荐 github 项目:GeminiImageApp(图片生成方向，可以做一定的素材)')
('https://www.cnblogs.com/jiaozai891/p/18912783', '.NET入行第4年（第一集）')
('https://www.cnblogs.com/seven97-top/p/18916370', '你了解Java线程池原理吗？')
('https://www.cnblogs.com/ITWeiHan/p/18920154', '自动 GitHub 20 种语言翻译工具 - OpenAiTx 开源免费')
('https://www.cnblogs.com/dennyLee2025/p/18919268', 'Java集合源码--ArrayList的可视化操作过程')
('https://www.cnblogs.com/token-ai/p/18920091', '（包含5w字提示词开源）手把手教你搭建开源提示词优化平台')
('https://www.cnblogs.com/apocelipes/p/18920038', 'Linux c 运行时获取动态库所在路径')
('https://www.cnblogs.com/aslanvon/p/18920022', 'CMAKE学习笔记')
('https://www.cnblogs.com/ayuday/p/18920003', 'Bootstrap Table强大的web数据表格渲染框架')
('https://www.cnblogs.com/SkyXZ/p/18919993', '机器人/智能车纯视觉巡线经典策略—滑动窗口+直方图法')
('https://www.cnblogs.com/piperliu/p/18919915', 'Go mod/work/get ... Golang 提供的项目管理工具该怎么用？')
('https://www.cnblogs.com/huangSir-devops/p/18919792', 'Jenkins概述及Linux系统中安装')
('https://www.cnblogs.com/wzrong/p/18919849', '产品修行录：一场关于产品与人生的深度探索')
('https://www.cnblogs.com/hnzhengfy/p/18910878/GrayRelease', '灰度发布简介')
('https://www.cnblogs.com/zhaloe/p/18919769', 'ArkUI-X与Android消息通信')
('https://www.cnblogs.com/k1115h0t/p/18919765', 'Java代码审计SpEL表达式注入')
('https://www.cnblogs.com/he-cheng/p/18908176', '未授权访问漏洞复现及总结')
('https://www.cnblogs.com/Javaer1995/p/18590450', 'CentOS-7 通过 NFS 实现服务器之间的文件（目录）共享')
('https://www.cnblogs.com/bokong/p/18919743', '在Linux下使用wxWidgets进行跨平台GUI开发')
('https://www.cnblogs.com/rossiXYZ/p/18916637', '探秘Transformer系列之（36）--- 大模型量化方案')
('https://www.cnblogs.com/yubaolee/p/18914864', '我的开源十年')
('https://www.cnblogs.com/blhmfL3c/p/18919563', '图解JavaScript原型：原型链及其分析 01 | JavaScript图解')
('https://www.cnblogs.com/wangerdan115/p/18919495', 'HarmonyOS运动语音开发：如何让运动开始时的语音播报更温暖')
('https://www.cnblogs.com/zsxblog/p/18919455', '重写IE的showModalDialog模态框以兼容现代浏览器')
('https://www.cnblogs.com/daimajiangxin/p/18919412', '从零开始学Flink：揭开实时计算的神秘面纱')
('https://www.cnblogs.com/fkxxgis/p/18919392', '数据分析必备：GPS轨迹、软件签到、手机信令数据获取方式合集')
('https://www.cnblogs.com/wJiang/p/18915252', 'Svelte 5状态管理实战：基于Tauri框架的AI阅读器Saga Reader开发实践')
('https://www.cnblogs.com/flyup/p/18919344', '图神经网络(GNN)模型的基本原理')
('https://www.cnblogs.com/youlanjihua/p/18919313', '详解鸿蒙Next仓颉开发语言中的动画')
('https://www.cnblogs.com/qizhou/p/18916604', '论文解读：Knowledge Neurons in Pretrained Transformers')
('https://www.cnblogs.com/shootdown/p/18919304', 'P1155 [NOIP 2008 提高组] 双栈排序')
('https://www.cnblogs.com/huangSir-devops/p/18908560', '一文搞懂K8s中的RBAC认证授权')
('https://www.cnblogs.com/wang_yb/p/18919251', '当机器学习遇见压缩感知：用少量数据重建完整世界')
('https://www.cnblogs.com/Can-daydayup/p/18919230', 'C#/.NET/.NET Core优秀项目和框架2025年5月简报')
('https://www.cnblogs.com/starlitnightly/p/18919221', '单细胞最好的教程(十八): 细胞类型映射到细胞本体论：让你的单细胞注释更专业！')
('https://www.cnblogs.com/aqi00/p/18916613', 'FFmpeg开发笔记（六十四）使用国产的RedPlayer播放器观看网络视频')
('https://www.cnblogs.com/carpell/p/18919164', '【语义分割专栏】3：Segnet原理篇')
('https://www.cnblogs.com/noxryn/p/-/vscodeset', 'VSCode安装配置C++环境教程')
('https://www.cnblogs.com/sesshoumaru/p/18919105/python-string-his', 'Python字符串进化史：从青涩到成熟的蜕变')
('https://www.cnblogs.com/Johny-zhao/p/18919080', '华为MAAS、阿里云PAI、亚马逊AWS SageMaker、微软Azure ML各大模型深度分析对比')
('https://www.cnblogs.com/yeyunfei/p/18916809', '如何基于three.js(webgl)引擎架构，实现3D密集架库房,3D档案室智能巡检')
('https://www.cnblogs.com/yby6/p/18917076', '玩转AI新声态 | 玩转TTS/ASR/YuanQI 打造自己的AI助手')
('https://www.cnblogs.com/kqdssheng/p/18916959', 'AD 提权-CVE-2022-26923: CertiFried')
('https://www.cnblogs.com/huiyichanmian/p/18916869', '使用scheduler-plugins实现自定义调度器')
('https://www.cnblogs.com/youlanjihua/p/18916815', '鸿蒙仓颉语言开发实战教程：商城应用个人中心页面')
('https://www.cnblogs.com/lmy5215006/p/18916344', 'C#代码如何影响CPU缓存速度？')
('https://www.cnblogs.com/huangSir-devops/p/18876361', '自签名证书工具cfssl详解')
('https://www.cnblogs.com/aqi00/p/18916610', 'FFmpeg开发笔记（六十三）FFmpeg使用vvenc把视频转为H.266编码')
('https://www.cnblogs.com/qizhou/p/18916580', '综述论文解读：Editing Large Language Models: Problems, Methods, and Opportunities')
('https://www.cnblogs.com/InCerry/p/-/introduce-cs-pattern-match', 'C# 模式匹配全解：原理、用法与易错点')
('https://www.cnblogs.com/wJiang/p/18915251', 'Svelte 5 在跨平台 AI 阅读助手中的实践：轻量化前端架构的极致性能优化')
('https://www.cnblogs.com/wzrong/p/18915221', '招标方案撰写的博弈：需求、成本与边界的三重修行')
('https://www.cnblogs.com/wrinkle-of-silicon/p/18915086', '对比分析LinkedBlockingQueue和SynchronousQueue')
('https://www.cnblogs.com/flyup/p/18915098', '知识图谱技术概述')
('https://www.cnblogs.com/GreenShade/p/18913382', '用纯.NET开发并制作一个智能桌面机器人（五）：使用.NET为树莓派开发Wifi配网功能')
('https://www.cnblogs.com/Can-daydayup/p/18903324', '2025 年实用、全面的 VS Code 插件推荐！')
('https://www.cnblogs.com/ludangxin/p/18913362', '2. LangChain4j-AIServices，原来调用AI这么简单？')
('https://www.cnblogs.com/lanfufu/p/18914905', '记一次诡异的线上异常赋值排查：代码没错，结果不对')
('https://www.cnblogs.com/k1115h0t/p/18914874', 'Java代码审计_RCE漏洞')
('https://www.cnblogs.com/chenyishi/p/18914866', 'pytorch入门 - 微调huggingface大模型')
('https://www.cnblogs.com/hekuan/p/18914494', '记一次SSD性能瓶颈排查之路——寿命与性能之间的取舍')
('https://www.cnblogs.com/Johny-zhao/p/18914456', '阿里云数据库Inventory Hint技术分析')
('https://www.cnblogs.com/hubert-style/p/18914446', '跨平台之 KMP / KMM 详解')
('https://www.cnblogs.com/sun-10387834/p/18914335', '【非对称加密】详解及Java实现')
('https://www.cnblogs.com/xiao987334176/p/18912784', 'dify打造数据可视化图表')
('https://www.cnblogs.com/zhangmingcheng/p/18914100', 'Token：大语言模型的“语言乐高”，一切智能的基石')
('https://www.cnblogs.com/aisong/p/18914138', '技术分享：主流GUI自动化框架的窗口置顶机制实现对比')
('https://www.cnblogs.com/lizhongzheng/p/18914089', 'QRSuperResolutionNet：一种结构感知与识别增强的二维码图像超分辨率网络（附代码解析）')
('https://www.cnblogs.com/wang_yb/p/18914035', '稀疏表示与字典学习：让数据“瘦身”的魔法')
('https://www.cnblogs.com/chingho/p/18913876', 'C#实现Stdio通信方式的MCP Server')
('https://www.cnblogs.com/qizhou/p/18913077', '论文解读：Aging with GRACE: Lifelong Model Editing with Discrete Key-Value Adapters')
('https://www.cnblogs.com/KubeExplorer/p/18913850', '开源 vGPU 方案：HAMi,实现细粒度 GPU 切分')
('https://www.cnblogs.com/dennyzhangdd/p/18913823', '架构师之我见（一）入门篇')
('https://www.cnblogs.com/jsccc520/p/18913731', '十步,做一个基于Claw Cloud的天气推送')
('https://www.cnblogs.com/cmt/p/18913712', '【故障公告】博客主站遭遇很奇怪的疯狂攻击')
('https://www.cnblogs.com/JuiceData/p/18913715', 'JuiceFS v1.3-Beta2：集成 Apache Ranger，实现更精细化的权限控制')
('https://www.cnblogs.com/MeteorSeed/p/18912257', '【译】Visual Studio 扩展管理器更新')
('https://www.cnblogs.com/ayuday/p/18913656', 'stylus - 新生代CSS预处理框架')
('https://www.cnblogs.com/cnsharp/p/18913607', '.NET SDK样式项目打包时如何将引用项目打进同一个包')
('https://www.cnblogs.com/youlanjihua/p/18913493', '鸿蒙仓颉语言开发教程：仓颉语言中的状态存储')
('https://www.cnblogs.com/wzrong/p/18913354', '需求评审冷场救星：3 招激活讨论氛围，挖掘真实反馈')
('https://www.cnblogs.com/ludangxin/p/18913321', '1. LangChain4j 初识，想使用Java开发AI应用？')
('https://www.cnblogs.com/aspnetx/p/18913301', '我与博客园的20年')
('https://www.cnblogs.com/InCerry/p/-/dotnet_week_25_5_4', '.NET周刊【5月第4期 2025-05-25】')
('https://www.cnblogs.com/sowler/p/18913269', 'SSH实现服务器之间免密登录')
('https://www.cnblogs.com/k1115h0t/p/18913107', 'Java代码审计_SQL注入')
('https://www.cnblogs.com/qizhou/p/18913058', '论文解读：Locating and Editing Factual Associations in GPT（ROME）')
('https://www.cnblogs.com/eventhorizon/p/18913041', '理解 .NET 结构体字段的内存布局')
('https://www.cnblogs.com/yupi/p/18912833', '刚刚，Cursor 1.0炸裂发布！4大亮点实战')
('https://www.cnblogs.com/danieldaren/p/18912626', '如何使用MCP开发一个客户端和服务端')
('https://www.cnblogs.com/aspnetx/p/18912609', 'Power BI回顾于2025年')
('https://www.cnblogs.com/vipstone/p/18912587', '超实用！Dify调用Java的3种实现方式！')
('https://www.cnblogs.com/dennyzhangdd/p/18832055', '一文搞懂国际化（三）落地实践')
('https://www.cnblogs.com/Firepad-magic/p/18912554', 'C# NativeAOT生成.so 库供Linux下C++调用')
('https://www.cnblogs.com/vivotech/p/18912401', 'vivo Pulsar万亿级消息处理实践（1）-数据发送原理解析和性能调优')
('https://www.cnblogs.com/chenyishi/p/18912364', 'pytorch入门 - LetNet5神经网络')
('https://www.cnblogs.com/hnu-hua/p/18912376', 'stm32cubemx+freertos+中断实现IIC从机')
('https://www.cnblogs.com/gmmy/p/18912354', 'ChatGPT Codex试用心得，码农的可靠助手or失业号角？')
('https://www.cnblogs.com/huggingface/p/18912328', 'SmolVLA: 让机器人更懂 “看听说做” 的轻量化解决方案')
('https://www.cnblogs.com/sdcb/p/18912317/20250605-sql-server-2025-preview-functions', 'SQL Server 2025 预览版新功能点评')
('https://www.cnblogs.com/HaiJun-Aion/p/18912201', '记录第一次公司内部分享：如何基于大模型搭建企业+AI业务')
('https://www.cnblogs.com/charlee44/p/18912102', 'GDAL 2.X升级3.X需要注意的问题总结')
('https://www.cnblogs.com/blog470130547/p/18912089', '《一个程序猿的生命周期》-《发展篇》- 47.用同样的思维模式做着不同的事')
('https://www.cnblogs.com/wang_yb/p/18912045', 'Manim中三种函数图像类的比较')
('https://www.cnblogs.com/jishuba/p/18912042', '决策树算法如何读懂你的购物心理？一文看懂背后的科学')
('https://www.cnblogs.com/huangxincheng/p/18911996', '聊一聊 .NET在Linux下的IO多路复用select和epoll')
('https://www.cnblogs.com/yupi/p/18911978', '全球首个无限执行的 AI 出现！给我玩爽了')
('https://www.cnblogs.com/hxjz/p/18910948', '开发十年现状之后端的职业发展')
('https://www.cnblogs.com/jevonsflash/p/18911657', '[学习笔记] 从零开始虚拟化搭建数据库服务器')
('https://www.cnblogs.com/springsource/p/18911607', '一个老程序员, 两个小时能用corsur做出什么样的东西')
('https://www.cnblogs.com/youlanjihua/p/18911529', '鸿蒙仓颉语言开发实战教程：商城搜索页')
('https://www.cnblogs.com/skyell/p/18911342', 'DeepSeek为什么现在感觉不火了？')
('https://www.cnblogs.com/BNTang/p/18911263', 'AI编码焕新：用Context7')
('https://www.cnblogs.com/wzrong/p/18910986', '需求评审不翻车指南：5 大沟通雷区与破解话术')
('https://www.cnblogs.com/InCerry/p/-/dotnet-9-exception-pref-improve', '.NET 9中的异常处理性能提升分析：为什么过去慢，未来快')
('https://www.cnblogs.com/hxjz/p/18910889', '开发十年现状之我的工作经历')
('https://www.cnblogs.com/token-ai/p/18910887', ' 革命性AI提示词优化平台正式开源！')
('https://www.cnblogs.com/kqdssheng/p/18910828', 'AD 横向移动-哈希传递攻击')
('https://www.cnblogs.com/cjdty/p/18910825', '[Python] 开发一个lychee相册命令行客户端以及python库pychee6')
('https://www.cnblogs.com/wqws/p/18910673', '让AI操作powershell会发生什么')
('https://www.cnblogs.com/sun-10387834/p/18910650', '【密码学中的数字摘要】概念、作用与示例')
('https://www.cnblogs.com/wdracky/p/18910480', '通义灵码2.5+qwen3——节假日抢票不用愁，基于12306-MCP实现个人火车票智能查询小助手！')
('https://www.cnblogs.com/abinzhao/p/18910353', 'HarmonyOS 实战：给笔记应用加防截图水印')
('https://www.cnblogs.com/feiyangqingyun/p/18910173', '垃圾qt，毁我青春')
('https://www.cnblogs.com/xiaobaiysf/p/18910051', 'Prompt 生产级提示词案例（含完整提示词）')
('https://www.cnblogs.com/vipsoft/p/18910003', 'Java 实现微信小程序不同人员生成不同小程序码并追踪扫码来源')
('https://www.cnblogs.com/linx/p/18909644', 'Web前端入门第 63 问：JavaScript 图解 for 循环执行顺序')
('https://www.cnblogs.com/code-daily/p/18909634', '揭秘C#异步编程核心机制：从状态机到线程池的全面拆解')
('https://www.cnblogs.com/powertoolsteam/p/18909586', 'GC-QA-RAG 智能问答系统的向量检索')
('https://www.cnblogs.com/xwwin/p/18909554', '使用Plop.js高效生成模板文件')
('https://www.cnblogs.com/carpell/p/18909515', '【语义分割专栏】2：U-net实战篇(附上完整可运行的代码pytorch)')
('https://www.cnblogs.com/wang_yb/p/18909497', 'manim边做边学--参数化曲线')
('https://www.cnblogs.com/seven97-top/p/18903349', 'Synchronized是怎么实现的？')
('https://www.cnblogs.com/youlanjihua/p/18909468', '鸿蒙仓颉语言开发实战教程：商城登录页')
('https://www.cnblogs.com/xiangji/p/18909458', 'ShadowSql.net之正确使用方式')
('https://www.cnblogs.com/xiezhr/p/18909452', '阿里也出手了！灵码AI IDE问世')
('https://www.cnblogs.com/Can-daydayup/p/18909325', '一种更简单的方式运行 C# 代码，简化 C# 开发体验！')
('https://www.cnblogs.com/zhennann/p/18909435', '你认为Vonajs提供的这些特性会比Nestjs更好用吗？')
('https://www.cnblogs.com/TwilightLemon/p/18909374', 'WPF 使用CompositionTarget.Rendering实现平滑流畅滚动的ScrollViewer，支持滚轮、触控板、触摸屏和笔')
('https://www.cnblogs.com/charlee44/p/18909212', '解决Vditor加载Markdown网页很慢的问题(Vite+JS+Vditor)')
('https://www.cnblogs.com/zhaloe/p/18909174', 'ArkUI-X中Plugin生命周期开发指南')
('https://www.cnblogs.com/zackstang/p/18908712', 'Strands Agents（一）Strands Agents 介绍')
('https://www.cnblogs.com/dechinphy/p/18908706/cls', 'Python中的cls变量')
('https://www.cnblogs.com/linx/p/18908725', 'Web前端入门第 62 问：JavaScript 循环结构注意事项')
('https://www.cnblogs.com/RTower/p/18908689', '经验帖：个人开发时请不要忽视前期业务逻辑和项目架构的设计')
('https://www.cnblogs.com/wangerdan115/p/18908683', 'HarmonyOS运动开发：精准估算室内运动的距离、速度与步幅')
('https://www.cnblogs.com/songmin/p/18908585/video-2channel-review-and-ocr-on-ohos', '【拥抱鸿蒙】HarmonyOS NEXT实现双路预览并识别文字')
('https://www.cnblogs.com/yexiaochai/p/18908519', '聊聊常见的几款Agent平台：字节Coze、腾讯元器、文心智能体')
('https://www.cnblogs.com/JacobX/p/18908334', '飞牛OS给容器魔方上行宽带限速')
('https://www.cnblogs.com/superconvert/p/18908213', '最快的流媒体服务器搭建 smart_rtmpd')
('https://www.cnblogs.com/12lisu/p/18908447', '高并发下如何防止商品超卖？')
('https://www.cnblogs.com/vipstone/p/18908161', '超实用！Dify快速接入本地MCP服务')
('https://www.cnblogs.com/yupi/p/18904399', '1 分钟生成架构图？程序员 AI 绘图保姆级教程')
('https://www.cnblogs.com/wang_yb/p/18908033', '机器学习中的"食材挑选术"：特征选择方法')
('https://www.cnblogs.com/carpell/p/18908044', '【语义分割专栏】2：U-net原理篇(由浅入深)')
('https://www.cnblogs.com/powertoolsteam/p/18908040', 'GC-QA-RAG 智能问答系统的文档切片')
('https://www.cnblogs.com/youlanjihua/p/18908005', '鸿蒙仓颉语言开发实战教程：购物车页面')
('https://www.cnblogs.com/zwwhnly/p/18907966', '聊聊@Autowired注解的Field injection is not recommended提示问题')
('https://www.cnblogs.com/seven97-top/p/18901367', 'Linux常用命令介绍-系统管理')
('https://www.cnblogs.com/sing1ee/p/18907929/a2a-samples-llama-index-file-chat-openrouter', '基于 A2A 协议的 LlamaIndex 文件聊天工作流')
('https://www.cnblogs.com/wubayue/p/18907899', '字符集、编码的前世今生')
('https://www.cnblogs.com/fanliang11/p/18907898', '凯亚物联网如何搭建信令服务')
('https://www.cnblogs.com/chaoguo1234/p/18907877', 'NSMutableDictionary 的内存布局')
('https://www.cnblogs.com/youlanjihua/p/18907716', '详解鸿蒙仓颉开发语言中的计时器')
('https://www.cnblogs.com/tianwuyvlianshui/p/18907695', 'ESP32S3内网实现 WebSocket')
('https://www.cnblogs.com/milton/p/18907603', '网心云 OEC/OECT 笔记(2) 运行RKNN程序')
('https://www.cnblogs.com/rossiXYZ/p/18903277', '探秘Transformer系列之（35）--- 大模型量化基础')
('https://www.cnblogs.com/tcjiaan/p/18907380', '【ASP.NET Core】调用 Web API 备份数据库')
('https://www.cnblogs.com/shanyou/p/18907518', '赋能企业应用开发者：无缝集成AI，无需转变编程语言')
('https://www.cnblogs.com/zpcdbky/p/18837914', '虚函数表里有什么？（四）——虚拟继承')
('https://www.cnblogs.com/wenbochang/p/18907453', '看到这种代码，我直接气到想打人')
('https://www.cnblogs.com/huangSir-devops/p/18907375', 'Prometheus配置文件详解')
('https://www.cnblogs.com/wang_yb/p/18907370', 'manim边做边学--隐函数图像')
('https://www.cnblogs.com/aser1989/p/18891962', 'Web性能优化：从 2 秒到200毫秒')
('https://www.cnblogs.com/smartloli/p/18907145', '如何实现RAG与MCP集成')
('https://www.cnblogs.com/Vanilla-chan/p/18907141', '哪张卡值得买？市面上部分显卡价格回归与预测')
('https://www.cnblogs.com/daen/p/18907134', '关于家庭宽带IPv6的开启、绑定域名、搭建网站、远程控制、使用教程等')
('https://www.cnblogs.com/cmt/p/18907031', '上周热点回顾（5.26-6.1）')
('https://www.cnblogs.com/Can-daydayup/p/18907012', 'C#/.NET/.NET Core技术前沿周刊 | 第 40 期（2025年5.26-5.31）')
('https://www.cnblogs.com/datacool/p/18906932', 'Winform高级技巧-界面和代码分离的实践案例')
('https://www.cnblogs.com/aqi00/p/18906906', 'FFmpeg开发笔记（六十二）Windows给FFmpeg集成H.266编码器vvenc')
('https://www.cnblogs.com/yfrs/p/18906865/java_read_last_line', 'JAVA实现读取最后几行日志')
('https://www.cnblogs.com/chaoguo1234/p/18906738', 'NSDictionary 内存布局')
('https://www.cnblogs.com/huangSir-devops/p/18859113', 'K8s新手系列之指定Pod调度到指定节点上')
('https://www.cnblogs.com/Can-daydayup/p/18906398', '精选 12 款开源、免费、美观的 Vue 后台管理系统模板！')
('https://www.cnblogs.com/wzrong/p/18906393', '小悟困惑：为啥大家愿意为粽子包装的仪式感买单？')
('https://www.cnblogs.com/fkxxgis/p/18906332', 'Ubuntu部署tensorflow（CPU/GPU）方法')
('https://www.cnblogs.com/Ctrl-cCtrl-v/p/18906192', 'RWKV-7 架构理解')
('https://www.cnblogs.com/Afeather/p/18906225', 'Ribbon过滤器原理解析')
('https://www.cnblogs.com/kelvin-cai/p/18906222', 'Spring Ai 从Demo到搭建套壳项目（一）初识与实现与deepseek对话模式')
('https://www.cnblogs.com/youlanjihua/p/18906187', '详解鸿蒙开发如何上传三方库到ohpm仓库')
('https://www.cnblogs.com/dongxb/p/18906169', 'X-MACRO使用技巧')
('https://www.cnblogs.com/huangSir-devops/p/18859120', 'Pod调度之亲和性')
('https://www.cnblogs.com/yfrs/p/18906051/oracle_dm', '信创-ORACLE迁移到DM8')
('https://www.cnblogs.com/tylerw/p/18879967', '【UEFI】HOB 从概念到代码')
('https://www.cnblogs.com/D1TA/p/18904007', '缓冲区溢出全解')
('https://www.cnblogs.com/coredx/p/18900966', '如何在 .NET 中构建一个好用的动态查询生成器')
('https://www.cnblogs.com/huangSir-devops/p/18903105', 'Redis持久化机制')
('https://www.cnblogs.com/ACaiGarden/p/18903988', '20250528 - Usual 攻击事件: 价差兑换与请君入瓮')
('https://www.cnblogs.com/linx/p/18903865', 'Web前端入门第 61 问：JavaScript 各种对象定义与对象取值方法')
('https://www.cnblogs.com/somefuture/p/18903818', '记录一次自己用 AI 写IOS APP的经历')
('https://www.cnblogs.com/apocelipes/p/18903417', 'golang遍历处理map时的常见性能陷阱')
('https://www.cnblogs.com/east7/p/18903584', 'JAVA JUC干货之线程池实现原理和源码详解（上）')
('https://www.cnblogs.com/wang_yb/p/18903581', 'Manim实现旋转变色特效')
('https://www.cnblogs.com/seven97-top/p/18894755', '你了解ConcurrentHashMap吗？ConcurrentHashMap九连问')
('https://www.cnblogs.com/vipstone/p/18903521', 'Dify搭建AI图片生成助手中的坑！')
('https://www.cnblogs.com/youlanjihua/p/18903512', '鸿蒙仓颉语言开发教程：网络请求和数据解析')
('https://www.cnblogs.com/jinjiangongzuoshi/p/18903503', '万字长文： 仅花7天，利用AI编程神器Cursor 从0到1开发上线个人网站，保姆级教程！')
('https://www.cnblogs.com/zengzuo613/p/18903459/lombok', 'Java 样板代码库 Lombok 使用详解')
('https://www.cnblogs.com/Afeather/p/18903455', 'C++用Mutex实现读写锁')
('https://www.cnblogs.com/piperliu/p/18903427', '我所理解的 Go 的 CSP 并发控制机制')
('https://www.cnblogs.com/flyup/p/18903402', '循环神经网络(RNN)模型')
('https://www.cnblogs.com/youlanjihua/p/18903011', 'HarmonyOS NEXT开发教程：全局悬浮窗')
('https://www.cnblogs.com/vivotech/p/18902941', '纯前端实现图片伪3D视差效果')
('https://www.cnblogs.com/fs7744/p/18902861', '已经在为VKProxy写UI配置站点和文档了')
('https://www.cnblogs.com/xiongze520/p/18902755', '.NET8带来的一些新特性')
('https://www.cnblogs.com/Serverless/p/18902807', 'MCP Server On FC 之旅第四站: 长连接闲置计费最高降低87%成本的技术内幕')
('https://www.cnblogs.com/-pyj/p/18902785', '功能测试进阶：从执行到设计的全链路解析 —— 写给技术人的深度实践指南')
('https://www.cnblogs.com/mingupupu/p/18902739', 'C#学习：构建一个更真实的基于LLM的简历评估系统')
('https://www.cnblogs.com/skyell/p/18902459', 'DeepSeek R1再进化：这次更新让它直接对标Claude 4')
('https://www.cnblogs.com/hsyluxiaoguo/p/18902335', '在LLVM中的greedy Register Allocation pass代码详解')
('https://www.cnblogs.com/lsjwq/p/18902156', 'SmolVLM2轻量级视频多模态模型，应用效果测评(风景、事故、仿真、统计、文字、识物)')
('https://www.cnblogs.com/1312mn/p/18899053', '.NET 开源工业视觉系统 OpenIVS 快速搭建自动化检测平台')
('https://www.cnblogs.com/songmin/p/18902001/flutter-plus-cursor-develop-ohos-app-2', '【拥抱鸿蒙】Flutter+Cursor轻松打造HarmonyOS应用（二）')
('https://www.cnblogs.com/code-daily/p/18899291', '.NET AI 基座双核引擎正式版发布：深度拆解 AI / Vector Extensions 如何重构企业级 AI 架构\u200b')
('https://www.cnblogs.com/ChanYanny/p/18901906', 'deepseek-r1的1.5b、7b、8b、14b、32b、70b和671b有啥区别？')
('https://www.cnblogs.com/wang_yb/p/18901822', 'Manim实现图像变形特效')
('https://www.cnblogs.com/xiangji/p/18901576', 'ShadowSql.net之表达式树')
('https://www.cnblogs.com/anai/p/18901501', 'RAG越来越不准？从Dify和ima知识库看元数据与标签如何让大模型更懂你')
('https://www.cnblogs.com/1314520xh/p/18901494', '第2讲、从启动到表单加载：Odoo 18 的完整执行流程详解')
('https://www.cnblogs.com/ggtop/p/18901426', 'Golang与Elasticsearch搭配检索运用')
('https://www.cnblogs.com/TwilightLemon/p/18901390', 'WPF 使用GDI+提取图片主色调并生成Mica材质特效背景')
('https://www.cnblogs.com/zhaloe/p/18901382', 'ArkUI-X添加到现有Android项目中')
('https://www.cnblogs.com/johnnyzen/p/18901093', '[Java/模板渲染引擎/技术选型] 模板引擎-技术调研')
('https://www.cnblogs.com/noear/p/18901214', '启用 Java AOT 编译打包 Solon 项目（Solon AOT）')
('https://www.cnblogs.com/kqdssheng/p/18901139', 'AD 横向移动-令牌模拟攻击')
('https://www.cnblogs.com/huggingface/p/18901095', '参加 Hugging Face 组织的 Gradio & MCP 智能体主题黑客松')
('https://www.cnblogs.com/rainbond/p/18901046', '传统企业如何玩转平台工程？2 个运维靠它管 50 + 应用')
('https://www.cnblogs.com/ggtc/p/18900372', '新能源电池壳冲压车间看板实施')
('https://www.cnblogs.com/PJRAWA/p/18900927', '从实际的编程示例中看i++与++i的区别')
('https://www.cnblogs.com/ahfuzhang/p/18900919', '后台服务器开发领域，还有什么值得爬的山')
('https://www.cnblogs.com/Countrymen/p/18900890', '基于 SSE、asp.net core razor 实现比分Live')
('https://www.cnblogs.com/fanfanfanlichun/p/18898883', '重磅开源 基于AI大语言模型的AI 助手全套开源解决方案 AI开源平台geekai-django')
('https://www.cnblogs.com/Jaryleely/p/18900768', '中年人到底犯了什么错？也许我们可以换一个活法')
('https://www.cnblogs.com/vipstone/p/18900774', '重磅！SpringBoot4发布，11项重大变更全解析！')
('https://www.cnblogs.com/bitzhuwei/p/18899755/rules-about-GLSL-preprocessor', 'GLSL的预处理器都有哪些规定？')
('https://www.cnblogs.com/huangSir-devops/p/18899281', 'systemctl服务文件管理指南')
('https://www.cnblogs.com/yexiaochai/p/18900607', '腾讯IMA VS 飞书知识问答：谁才是2025最强AI知识库？')
('https://www.cnblogs.com/seatunnel/p/18900580', '【异常总结】SeaTunnel集群脑裂配置优化方法')
('https://www.cnblogs.com/songmin/p/18900380/flutter-plus-cursor-to-develop-ohos-app', '【拥抱鸿蒙】Flutter+Cursor轻松打造HarmonyOS应用（一）')
('https://www.cnblogs.com/huangxincheng/p/18900163', '聊一聊 C# NativeAOT 多平台下的函数导出')
('https://www.cnblogs.com/linx/p/18900100', 'Web前端入门第 60 问：JavaScript 各种数组定义与数组取值方法')
('https://www.cnblogs.com/w4ngzhen/p/18899988', '从零开发Vim-like编辑器（01）起步')
('https://www.cnblogs.com/WindrunnerMax/p/18899981', '从零实现富文本编辑器#4-浏览器选区模型的核心交互策略')
('https://www.cnblogs.com/wang_yb/p/18899917', '度量学习：让机器学会“距离”的奥秘')
('https://www.cnblogs.com/powertoolsteam/p/18899871', 'AI对低代码技术的影响')
('https://www.cnblogs.com/wangerdan115/p/18899774', '鸿蒙运动开发实战：打造 Keep 式轨迹播放效果')
('https://www.cnblogs.com/depend/p/18899765', '游戏开发godot+mcp等于事半功倍，分享一下如何安装godot相关的mcp及有何作用')
('https://www.cnblogs.com/xueweihan/p/18899586', '《HelloGitHub》第 110 期')
('https://www.cnblogs.com/TS86/p/18899677', 'AI赋能金融风控：基于机器学习的智能欺诈检测系统实战教程')
('https://www.cnblogs.com/FreakEmbedded/p/18899634', '普通继电器 vs 磁保持继电器 vs MOS管：工作原理与电路设计全解析')
('https://www.cnblogs.com/wzrong/p/18899418', '小悟问：用户说 “想要一匹更快的马”，是在骗我吗？')
('https://www.cnblogs.com/InCerry/p/-/dotnet_week_25_5_3', '.NET周刊【5月第3期 2025-05-18】')
('https://www.cnblogs.com/ENchantedN/p/18899492', '计算机图形学——Games101深度解析_第一章')
('https://www.cnblogs.com/yueyanWZF/p/18899420', '2025 CCPC打铁记')
('https://www.cnblogs.com/densen2014/p/18899403', 'Github Copilot 实战: 从零开始用AI写一个OCR工具 (3)')
('https://www.cnblogs.com/Yygz314/p/18899387', '中国象棋小游戏（C版）')
('https://www.cnblogs.com/TS86/p/18899282', '基于AI的智能农业病虫害识别系统实战指南')
('https://www.cnblogs.com/henjay724/p/18899171', '痞子衡嵌入式：i.MXRT10xx系列ROM的UART SDP设置不同波特率的方法与实践')
('https://www.cnblogs.com/token-ai/p/18898794', 'OpenDeepWiki：让您的代码仓库拥有MCP变成Agents的一部分！！')
('https://www.cnblogs.com/lsjwq/p/18898686', '微软开源bitnet b1.58大模型，应用效果测评(问答、知识、数学、逻辑、分析)')
('https://www.cnblogs.com/xiuqian/p/18898640', '前端预览和打印PDF的两种方式')
('https://www.cnblogs.com/mjunz/p/18898587', '秒杀系统—1.架构设计和方案简介')
('https://www.cnblogs.com/linx/p/18898533', 'Web前端入门第 59 问：JavaScript 条件语句中善用 return 让代码更清晰')
('https://www.cnblogs.com/ayuday/p/18898519', 'algolia使用配置教程-为SSG静态站增加algolia搜索功能')
('https://www.cnblogs.com/yupi/p/18898437', 'Claude 4炸裂发布！凭什么敢称宇宙最强编程 AI？')
('https://www.cnblogs.com/charlee44/p/18898397', '通过JS模板引擎实现动态模块组件(Vite+JS+Handlebars)')
('https://www.cnblogs.com/du-hong/p/18686315', 'PC端自动化测试实战教程-7-pywinauto等待方法大集合 （详细教程）')
('https://www.cnblogs.com/huangxincheng/p/18898303', '聊一聊 .NET Dump 中的 Linux信号机制')
('https://www.cnblogs.com/thanks/p/18898273', 'VsCode+DeepSeek的AI编程助手初体验')
('https://www.cnblogs.com/gerry-wrl/p/18898109', 'PostGIS栅格数据类型解析【raster】')
('https://www.cnblogs.com/vipstone/p/18898217', 'AI智能体策略FunctionCalling和ReAct有什么区别？')
('https://www.cnblogs.com/huangSir-devops/p/18895344', 'K8s进阶之多租户场景下的资源配额（ResourceQuota）')
('https://www.cnblogs.com/code-daily/p/18898124', '单一职责原则的思维：为什么你的代码总在“牵一发而动全身”')
('https://www.cnblogs.com/12lisu/p/18898130', '明明是同一条SQL，为什么有时候走索引a，有时候却走索引b ？')
('https://www.cnblogs.com/MeteorSeed/p/18892725', '【译】微软与 Anthropic 合作为 MCP 创建官方 C# SDK')
('https://www.cnblogs.com/youlanjihua/p/18898005', '鸿蒙仓颉开发语言实战教程：自定义组件')
('https://www.cnblogs.com/chingho/p/18897980', 'C#实现SSE通信方式的MCP Server')
('https://www.cnblogs.com/seven97-top/p/18892482', 'Linux常用命令介绍-文档编辑')
('https://www.cnblogs.com/HarmonyOS5/p/18897113', '鸿蒙版微信小程序不可用，一文告诉你10分钟修复')
('https://www.cnblogs.com/wang_yb/p/18897904', '降维技术：带你走进数据的“瘦身”世界')
('https://www.cnblogs.com/xiezhr/p/18897864', 'SpringBoot3整合SpringSecurity6(五)自定义登陆页面')
('https://www.cnblogs.com/gogoSandy/p/18897805', '解密prompt系列54.Context Cache代码示例和原理分析')
('https://www.cnblogs.com/densen2014/p/18897768', 'Github Copilot 实战: 从零开始用AI写一个OCR工具 (1)')
('https://www.cnblogs.com/jvxiao/p/18897685', '搭建个人博客系列--(1) 为什么每个人都该有个数字自留地')
('https://www.cnblogs.com/youlanjihua/p/18897384', '鸿蒙仓颉开发语言实战教程：自定义tabbar')
('https://www.cnblogs.com/TS86/p/18897366', '基于Photon与Unreal Engine的VR协作平台开发实战教程')
('https://www.cnblogs.com/Jaryleely/p/18897212', '40岁后，想清楚这5件事，比努力更重要')
('https://www.cnblogs.com/ZYPLJ/p/18897174', '简单说说C#中委托的使用-01')
('https://www.cnblogs.com/blbl-blog/p/18897109', '如何通过接口实现动态二维码的定时刷新')
('https://www.cnblogs.com/zhanggaoxing/p/18897086', ' Python f-string 全攻略：从入门到大师，让你的编码效率翻倍！')
('https://www.cnblogs.com/68786C/p/18897022', '华为手环6拆解与学习')
('https://www.cnblogs.com/Johny-zhao/p/18896996', 'Kali Linux 从入门到实战：系统详解与工具指南')
('https://www.cnblogs.com/wangerdan115/p/18896828', 'HarmonyOS运动开发：如何绘制运动速度轨迹')
('https://www.cnblogs.com/cmt/p/18896812', '上周热点回顾（5.19-5.25）')
('https://www.cnblogs.com/flyup/p/18896762', '卷积神经网络(CNN)模型')
('https://www.cnblogs.com/huangSir-devops/p/18895502', 'K8s进阶之LimitRange')
('https://www.cnblogs.com/deali/p/18896645', 'SharpIco：用纯C#打造零依赖的.ico图标生成器，支持.NET9与AOT编译')
('https://www.cnblogs.com/wang_yb/p/18896593', '不同数据场景下的聚类算法')
('https://www.cnblogs.com/linx/p/18896521', 'Web前端入门第 58 问：JavaScript 运算符 == 和 === 有什么区别？')
('https://www.cnblogs.com/chingho/p/18896462', 'C#实现MCP Client 与 LLM 连接，抓取网页内容功能！')
('https://www.cnblogs.com/wzrong/p/18896413', '产品修行录：电动车通勤中的思考')
('https://www.cnblogs.com/apocelipes/p/18896209', 'golang unsafe遇上字符串拼接优化导致的bug')
('https://www.cnblogs.com/jijunjian/p/18896403', 'AI工程师跑路了-SpringAi来帮忙')
('https://www.cnblogs.com/wJiang/p/18895168', '【实战】Rust与前端协同开发：基于Tauri的跨平台AI阅读器实践')
('https://www.cnblogs.com/jinjiangongzuoshi/p/18896289', '利用DeepSeek与Python自动生成测试用例！')
('https://www.cnblogs.com/mjunz/p/18896162', 'Disruptor—3.核心源码实现分析')
('https://www.cnblogs.com/bianyibushu/p/18896046', 'Serial-Studio 上位机编译全过程深度讲解，解决串口数据可视化工具')
('https://www.cnblogs.com/wenbochang/p/18895509', 'Spring异常处理 bug ！！！同一份代码，结果却不一样？')
('https://www.cnblogs.com/TS86/p/18895996', '开发AR导航助手：ARKit+Unity+Mapbox全流程实战教程')
('https://www.cnblogs.com/Big-Yellow/p/18895944', 'CV中常用Backbone-3：Clip/SAM原理以及代码操作')
('https://www.cnblogs.com/youlanjihua/p/18895769', '鸿蒙仓颉开发语言实战教程：页面跳转和传参')
('https://www.cnblogs.com/xuzeyu/p/18895717', '【AI实战】从“苦AI”到“爽AI”：Magentic-UI 把“人类-多智能体协作”玩明白了！')
('https://www.cnblogs.com/shanyou/p/18895698', '.NET 10 进展之  CoreCLR Interpreter')
('https://www.cnblogs.com/MowMark/p/18895687', '适用于Ventoy和VirtualBox的WinToGo系统制作教程')
('https://www.cnblogs.com/12lisu/p/18895475', 'SpringBoot性能优化的12个小技巧')
('https://www.cnblogs.com/wang_yb/p/18895359', '轻松掌握Manim的.animate语法：让动画编程更简单')
('https://www.cnblogs.com/xiaokang-coding/p/18895297', '「硬核科普」C++11锁机制三兄弟大比拼：mutex、lock_guard与unique_lock')
('https://www.cnblogs.com/aqi00/p/18895292', 'FFmpeg开发笔记（六十一）Linux给FFmpeg集成H.266编码器vvenc')
('https://www.cnblogs.com/lmy5215006/p/18894045', '浅谈.NET微服务架构的演变')
('https://www.cnblogs.com/wzrong/p/18895236', '周末带娃悟出的产品思维：每个孩子都是 “用户体验” 的考官')
('https://www.cnblogs.com/yupi/p/18895197', '炸裂！Spring AI 1.0 正式发布，让 Java 再次伟大！')
('https://www.cnblogs.com/Can-daydayup/p/18895094', '全网最全！1500+ 免费、美观的前端网页模板，建站神器（包括HTML、Vue、Angular、React等）！')
('https://www.cnblogs.com/Johny-zhao/p/18895046', 'Nmap 从入门到精通：详细指南')
('https://www.cnblogs.com/sheng_chao/p/18905951', '独立开发者的在线客服系统：从 0 到 300 余万次会话，1700 余万条消息')
('https://www.cnblogs.com/wang_yb/p/18905882', 'manim边做边学--显函数图像')
('https://www.cnblogs.com/youlanjihua/p/18905818', '鸿蒙仓颉语言开发教程：自定义弹窗')
('https://www.cnblogs.com/ChebyshevTST/p/18905688', 'REVM移植小记')
('https://www.cnblogs.com/milton/p/18904750', '3D Gaussian splatting 04: 代码阅读-提取相机位姿和稀疏点云')
('https://www.cnblogs.com/smartloli/p/18905572', '如何实现本地大模型与MCP集成')
('https://www.cnblogs.com/huangSir-devops/p/18905316', 'K8s新手系列之探针')
('https://www.cnblogs.com/youzhibing/p/18905471', '安全漏洞修复导致SpringBoot2.7与Springfox不兼容，问题排查与处理')
('https://www.cnblogs.com/milton/p/18904928', '网心云 OEC/OECT 笔记(1) 拆机刷入Armbian固件')
('https://www.cnblogs.com/youlanjihua/p/18905310', '鸿蒙仓颉语言开发实战教程：实现商品分类页')
('https://www.cnblogs.com/huangSir-devops/p/18900643', 'K8s集群中的DNS服务（CoreDNS）详解')
('https://www.cnblogs.com/tianwuyvlianshui/p/18905215', 'ESP32掌控终端项目（详细+长篇+源码）')
('https://www.cnblogs.com/flyup/p/18905050', '长短期记忆（LSTM）网络模型')
('https://www.cnblogs.com/charlee44/p/18904937', '使用Vditor将Markdown文档渲染成网页(Vite+JS+Vditor)')
('https://www.cnblogs.com/huangSir-devops/p/18904053', 'Redis主从复制详解')
('https://www.cnblogs.com/rainbond/p/18904881', '鲲鹏Arm+麒麟V10，国产化信创 K8s 离线部署保姆级教程')
('https://www.cnblogs.com/milton/p/18904741', '3D Gaussian splatting 03: 用户数据训练和结果查看')
('https://www.cnblogs.com/TS86/p/18904816', '智能教育个性化学习路径规划系统实战指南')
('https://www.cnblogs.com/kqdssheng/p/18904760', 'AD 横向移动-SMB 中继攻击')
('https://www.cnblogs.com/sun-10387834/p/18904611', '【对称加密】DES与AES算法详解及Java实现')
('https://www.cnblogs.com/xuxueli/p/18894826', 'XXL-MQ v1.4.0 | 轻量级分布式消息队列')
('https://www.cnblogs.com/mjunz/p/18894823', 'Disruptor—2.并发编程相关简介')
('https://www.cnblogs.com/TS86/p/18894803', '金融科技应用：基于XGBoost与SHAP的信用评分模型构建全流程解析')
('https://www.cnblogs.com/xiaokang-coding/p/18894717', '「C++黑魔法」future与promise：不加锁的异步编程，原来可以这么简单！')
('https://www.cnblogs.com/huangSir-devops/p/18871607', 'K8s新手系列之DaemonSet资源')
('https://www.cnblogs.com/tianwuyvlianshui/p/18894686', 'ESP32S3实现Web服务器')
('https://www.cnblogs.com/kqdssheng/p/18894663', 'AD 权限维持-金票银票攻击')
('https://www.cnblogs.com/johnnyzen/p/18894419', '[Redis] Redis (7) 连接与会话管理')
('https://www.cnblogs.com/weiyuanzhang/p/18894564', 'X86C++反汇编01.IDA和提取签名')
('https://www.cnblogs.com/Sun-Wind/p/18894486', 'stable diffusion论文解读')
('https://www.cnblogs.com/changelzj/p/18893870', 'SQL解析工具JSQLParser')
('https://www.cnblogs.com/fkxxgis/p/18894392', '部署可使用GPU的tensorflow库')
('https://www.cnblogs.com/huangSir-devops/p/18891913', 'K8s中的污点和容忍')
('https://www.cnblogs.com/rossiXYZ/p/18889724', '探秘Transformer系列之（34）--- 量化基础')
('https://www.cnblogs.com/javadaydayup/p/18894332', '一个 Bean 就这样走完了它的一生之 Bean 的出生')
('https://www.cnblogs.com/huangxincheng/p/18894299', '.NET外挂系列：8. harmony 的IL编织 Transpiler')
('https://www.cnblogs.com/chenyishi/p/18894206', 'WinDbg 分析 .NET Dump 线程锁问题')
('https://www.cnblogs.com/shanyou/p/18894166', 'SuperSocket 2.0 的发布标志着.NET Socket 服务器框架迈入了一个全新的时代')
('https://www.cnblogs.com/InCerry/p/-/dotnet_week_25_5_2', '.NET周刊【5月第2期 2025-05-11】')
('https://www.cnblogs.com/xiaoyan2017/p/18894117', 'flutter3-deepseek流式AI模板|Flutter3.27+Dio+DeepSeeek聊天ai助手')
('https://www.cnblogs.com/TheMagicalRainbowSea/p/18894075', '秒杀/高并发解决方案+落地实现 (技术栈: SpringBoot+Mysql + Redis +RabbitMQ +MyBatis-Plus +Maven + Linux + Jmeter )-01')
('https://www.cnblogs.com/TonyCode/p/18893971', '游戏中常用的平滑曲线函数：高中生也能看懂的代码分析')
('https://www.cnblogs.com/bfmhno3/p/18893864', '【踩坑】VMware Workstation 17.x 中的虚拟机按键反映迟钝')
('https://www.cnblogs.com/changelzj/p/18893845', 'TenantLineInnerInterceptor源码解读')
('https://www.cnblogs.com/Can-daydayup/p/18893770', '一个使用 WPF 开发的 Diagram 画板工具（包含流程图FlowChart，思维导图MindEditor）')
('https://www.cnblogs.com/AikN/p/18893668', '全网第二细致的Verl GRPO实现拆解讲解')
('https://www.cnblogs.com/wrinkle-of-silicon/p/18893608', 'MySQL的表空间释放')
('https://www.cnblogs.com/vipstone/p/18893618', '必看！手把手教你玩转Dify的3大核心工具！')
('https://www.cnblogs.com/enjoyall/p/18893519', '记录一次maven依赖冲突的解决')
('https://www.cnblogs.com/mjunz/p/18893461', 'Disruptor—1.原理和使用简介')
('https://www.cnblogs.com/klri/p/18893334', 'VSCode配置c++环境速通')
('https://www.cnblogs.com/huangxincheng/p/18893322', '.NET外挂系列：7. harmony在高级调试中的一些实战案例')
('https://www.cnblogs.com/mazhimazhi/p/18877166', 'JDK网站最终的拼图')
('https://www.cnblogs.com/noear/p/18893165', 'Solon AI 正试发布（支持 java8+，RAG，MCP）')
('https://www.cnblogs.com/Serverless/p/18893134', 'MCP Server 实践之旅第 3 站：MCP 协议亲和性的技术内幕')
('https://www.cnblogs.com/nianzhilian/p/18892950', '迭代、迭代器、生成器的前世今生')
('https://www.cnblogs.com/xiao987334176/p/18891735', 'ComfyUI+通义万相 Wan2.1系列生成视频教程')
('https://www.cnblogs.com/wang_yb/p/18892531', '聚类是如何度量数据间的“远近”的？')
('https://www.cnblogs.com/xiaorong/p/18892424', 'frp增加IP限制')
('https://www.cnblogs.com/huangxincheng/p/18892412', '.NET外挂系列：6. harmony中一些实用的反射工具包')
('https://www.cnblogs.com/lyhabc/p/18692856/efficient-data-retrieval-methods-in-postgresql-vs-sql-server', '为何PostgreSQL没有聚集索引？解读两大数据库的设计差异')
('https://www.cnblogs.com/wubayue/p/18892131', 'UML类图-UML Class Diagram')
('https://www.cnblogs.com/xiaoxi666/p/18892107', '好端端的线程池，怎么就卡死了？')
('https://www.cnblogs.com/charlee44/p/18892006', '实现一个前端动态模块组件(Vite+原生JS)')
('https://www.cnblogs.com/Can-daydayup/p/18891942', '一个基于 C# 编写的事件驱动、具备专业水准的算法交易平台（量化交易引擎）')
('https://www.cnblogs.com/vipstone/p/18891936', '实战：Dify智能体+Java=自动化运营工具！')
('https://www.cnblogs.com/youlanjihua/p/18891926', '鸿蒙仓颉开发语言实战教程：实现商城应用首页')
('https://www.cnblogs.com/TS86/p/18891900', '基于Scikit-learn与Flask的医疗AI糖尿病预测系统开发实战')
('https://www.cnblogs.com/MoyuAndHan/p/18891870', 'excel/wps, 转code128字体宏, 部分字符串出现空格, 导致条码断裂无法扫描的解决方案')
('https://www.cnblogs.com/kqdssheng/p/18891731', 'AD 横向移动-TGS-REP Kerberoasting 攻击')
('https://www.cnblogs.com/yexiaochai/p/18891628', 'Google I/O 详细解读')
('https://www.cnblogs.com/sunstrikes/p/18891538', 'SgLang代码细读-3.Cache')
('https://www.cnblogs.com/springsource/p/18891362', '会用 AI 的工程师，效率已经拉开差距了 - “ 我们曾经引以为傲的编码能力，正在被改写。”')
('https://www.cnblogs.com/lmy5215006/p/18877083', 'C#网络编程(六)----Socket编程模型')
('https://www.cnblogs.com/somefuture/p/18889281', 'GIM发布新版本了 （附rust CLI制作brew bottle流程）')
('https://www.cnblogs.com/chingho/p/18891026', 'VS Code + Cline + 魔搭MCP Server 实现抓取网页内容。')
('https://www.cnblogs.com/vivotech/p/18890941', 'vivo官网APP首页端智能业务实践')
('https://www.cnblogs.com/huangSir-devops/p/18890598', 'Redis配置文件详解')
('https://www.cnblogs.com/qq21497936/p/18890352', 'GStreamer开发笔记（四）：ubuntu搭建GStreamer基础开发环境以及基础Demo')
('https://www.cnblogs.com/code-daily/p/18886677', 'C#线程池核心技术：从原理到高效调优的实用指南')
('https://www.cnblogs.com/linx/p/18890277', 'Web前端入门第 57 问：JavaScript 数据类型与类型转换')
('https://www.cnblogs.com/carpell/p/18890267', '【语义分割专栏】：FCN实战篇(附上完整可运行的代码pytorch)')
('https://www.cnblogs.com/wang_yb/p/18890142', 'Manim动画渲染：从代码到屏幕的幕后故事')
('https://www.cnblogs.com/MeteorSeed/p/18888581', '【译】Visual Studio 2022 v17.14 现已正式发布！')
('https://www.cnblogs.com/Johny-zhao/p/18890099', 'VMware Workstation 部署企业级 AD 域、DNS、DHCP 系统操作指南')
('https://www.cnblogs.com/mjunz/p/18890093', 'Seata源码—9.Seata XA模式的事务处理')
('https://www.cnblogs.com/seven97-top/p/18880996', '假设有一个 1G 大的 HashMap，此时用户请求过来刚好触发它的扩容，会怎样？')
('https://www.cnblogs.com/hez2010/p/18889954/the-new-satori-gc-for-dotnet', '.NET 的全新低延时高吞吐自适应 GC - Satori GC')
('https://www.cnblogs.com/huangxincheng/p/18889814', '.NET外挂系列：5. harmony 中补丁参数的有趣玩法（下）')
('https://www.cnblogs.com/xiaokang-coding/p/18889729', '玩转C++11多线程：让你的程序飞起来的std::thread终极指南')
('https://www.cnblogs.com/sinizu/p/18889651', '搭建阅读linux源码的舒适环境（vscode+clangd）')
('https://www.cnblogs.com/charlee44/p/18889653', '使用Vite创建一个动态网页的前端项目')
('https://www.cnblogs.com/goldsunshine/p/18889537', 'f-string 高效的字符串格式化')
('https://www.cnblogs.com/vipstone/p/18889533', 'Spring AI 1.0 正式发布！核心内容和智能体详解')
('https://www.cnblogs.com/zhanggaoxing/p/18889516', '张高兴的大模型开发实战：（六）在 LangGraph 中使用 MCP 协议')
('https://www.cnblogs.com/I14514/p/18889315', '玩转代码：深入GitHub，高效管理我们的“shou学”平台源代码')
('https://www.cnblogs.com/Johny-zhao/p/18889060', '线下IDC数据中心迁移至阿里云详细方案')
('https://www.cnblogs.com/bronya0/p/18889023', '基于顶级编解码器实现纯前端高效图片压缩')
('https://www.cnblogs.com/youlanjihua/p/18888969', '详解鸿蒙仓颉开发语言中的日志打印问题')
