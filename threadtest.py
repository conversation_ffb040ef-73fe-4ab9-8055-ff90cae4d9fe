from concurrent.futures import ThreadPoolExecutor
import threading
import concurrent
import time
def worker():
    print("Worker thread started")
    time.sleep(2)
    print("Worker thread finished")

def main():
    pool = ThreadPoolExecutor(max_workers=3)
    for i in range(5):
        pool.submit(worker)
    pool.shutdown()


def pooltest():
    def task(n):
        print(f"处理任务n:{n}")
        time.sleep(1)
        return n*n
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(task,i)for i in range(3)]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            logger.info(f"任务结果: {result}")
        



if __name__ == "__main__":
    # 创建并启动线程
    thread = threading.Thread(target=worker)
    thread.start()
    
    # 主线程继续执行
    print("Main thread doing other work...")
    time.sleep(1)
    
    # 等待子线程完成
    thread.join()
    
    main()
    pooltest()
    
    print("Main thread finished")
