import time
import threading
import blog_spider

def single_handler():
    for url in blog_spider.urls:
        r =blog_spider.crawler(url)


def mult_thread():
    threads = []
    for url in blog_spider.urls:
        threads.append(
            threading.Thread(target=blog_spider.crawler,args=(url,))
        )
    for thread in threads:
        thread.start()
    for thread in threads:
        thread.join()

import asyncio
if __name__ == '__main__':
    start_time = time.time()
    single_handler()
    end_time = time.time()
    print(f'single_handler程序执行时间：{end_time - start_time}')

    start_time = time.time()
    mult_thread()
    end_time = time.time()
    print(f'mult_thread程序执行时间{end_time - start_time}')

    start_time = time.time()
    asyncio.run(blog_spider.crawler_async())
    end_time = time.time()
    print(f'blog_spider_async程序执行时间{end_time - start_time}')

