import blog_spider
from concurrent.futures import ThreadPoolExecutor, as_completed

with ThreadPoolExecutor() as pool:
    futures = pool.map(blog_spider.crawler, blog_spider.urls)
    htmls = list(zip(blog_spider.urls, futures))
    for url,html in htmls:
        print(url, len(html))


with ThreadPoolExecutor() as pool:
    futures ={}
    for url,html in htmls:
       future =  pool.submit(blog_spider.parser, html)
       futures[future] = url

    for future in as_completed(futures):
        url = futures[future]
        html = future.result()
        print(url, html)
