"""
Playwright 跨线程解决方案
由于 Playwright 不支持跨线程使用，这里提供几种解决方案
"""

import asyncio
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Any, Callable, Dict, List
import json

# 需要安装: pip install playwright

# ============================================================================
# 方案1: 使用队列在主线程中运行Playwright
# ============================================================================

class PlaywrightWorker:
    """在单独线程中运行Playwright的工作器"""
    
    def __init__(self):
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
    
    def start(self):
        """启动工作线程"""
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop)
        self.worker_thread.start()
    
    def stop(self):
        """停止工作线程"""
        self.running = False
        self.task_queue.put(None)  # 发送停止信号
        if self.worker_thread:
            self.worker_thread.join()
    
    def _worker_loop(self):
        """工作线程主循环"""
        # 在这个线程中初始化Playwright
        try:
            # 这里模拟Playwright初始化
            print("Playwright工作线程启动")
            
            while self.running:
                try:
                    task = self.task_queue.get(timeout=1)
                    if task is None:  # 停止信号
                        break
                    
                    task_id, func, args, kwargs = task
                    try:
                        result = func(*args, **kwargs)
                        self.result_queue.put((task_id, 'success', result))
                    except Exception as e:
                        self.result_queue.put((task_id, 'error', str(e)))
                    
                except queue.Empty:
                    continue
                    
        except Exception as e:
            print(f"Playwright工作线程错误: {e}")
        finally:
            print("Playwright工作线程结束")
    
    def execute_task(self, func: Callable, *args, **kwargs) -> Any:
        """执行任务并等待结果"""
        task_id = id(threading.current_thread())
        self.task_queue.put((task_id, func, args, kwargs))
        
        # 等待结果
        while True:
            try:
                result_task_id, status, result = self.result_queue.get(timeout=10)
                if result_task_id == task_id:
                    if status == 'success':
                        return result
                    else:
                        raise Exception(result)
            except queue.Empty:
                raise TimeoutError("任务执行超时")

# 模拟Playwright操作函数
def simulate_playwright_scrape(url: str) -> Dict[str, Any]:
    """模拟Playwright爬取操作"""
    print(f"模拟爬取 {url}")
    time.sleep(1)  # 模拟网络请求
    return {
        'url': url,
        'title': f'Title of {url}',
        'content': f'Content from {url}',
        'timestamp': time.time()
    }

def simulate_playwright_screenshot(url: str, filename: str) -> str:
    """模拟Playwright截图操作"""
    print(f"模拟截图 {url} -> {filename}")
    time.sleep(0.5)
    return f"Screenshot saved to {filename}"

# ============================================================================
# 方案2: 异步队列处理多个Playwright任务
# ============================================================================

class AsyncPlaywrightManager:
    """异步Playwright管理器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.task_queue = asyncio.Queue()
        self.workers = []
        self.running = False
    
    async def start(self):
        """启动工作器"""
        self.running = True
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"Worker-{i}"))
            self.workers.append(worker)
    
    async def stop(self):
        """停止所有工作器"""
        self.running = False
        
        # 发送停止信号给所有工作器
        for _ in range(self.max_workers):
            await self.task_queue.put(None)
        
        # 等待所有工作器完成
        await asyncio.gather(*self.workers)
    
    async def _worker(self, worker_name: str):
        """工作器协程"""
        print(f"{worker_name} 启动")
        
        while self.running:
            try:
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                if task is None:  # 停止信号
                    break
                
                future, func, args, kwargs = task
                try:
                    # 在这里可以初始化每个工作器的Playwright实例
                    result = await self._execute_playwright_task(func, *args, **kwargs)
                    future.set_result(result)
                except Exception as e:
                    future.set_exception(e)
                
            except asyncio.TimeoutError:
                continue
        
        print(f"{worker_name} 结束")
    
    async def _execute_playwright_task(self, func: Callable, *args, **kwargs) -> Any:
        """执行Playwright任务"""
        # 这里可以使用真正的Playwright
        # 目前使用模拟函数
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    
    async def submit_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交任务"""
        future = asyncio.Future()
        await self.task_queue.put((future, func, args, kwargs))
        return await future

# ============================================================================
# 方案3: 进程池处理Playwright（推荐用于CPU密集型任务）
# ============================================================================

from multiprocessing import Process, Queue as MPQueue, Manager
import multiprocessing as mp

def playwright_worker_process(task_queue: MPQueue, result_queue: MPQueue, worker_id: int):
    """在独立进程中运行Playwright"""
    print(f"Playwright进程 {worker_id} 启动")
    
    # 在这里初始化Playwright（每个进程有自己的实例）
    # from playwright.sync_api import sync_playwright
    
    while True:
        try:
            task = task_queue.get(timeout=1)
            if task is None:  # 停止信号
                break
            
            task_id, func_name, args, kwargs = task
            
            try:
                # 根据函数名执行相应操作
                if func_name == 'scrape':
                    result = simulate_playwright_scrape(*args, **kwargs)
                elif func_name == 'screenshot':
                    result = simulate_playwright_screenshot(*args, **kwargs)
                else:
                    result = f"Unknown function: {func_name}"
                
                result_queue.put((task_id, 'success', result))
                
            except Exception as e:
                result_queue.put((task_id, 'error', str(e)))
                
        except:
            continue
    
    print(f"Playwright进程 {worker_id} 结束")

class PlaywrightProcessPool:
    """Playwright进程池"""
    
    def __init__(self, num_processes: int = 2):
        self.num_processes = num_processes
        self.task_queue = MPQueue()
        self.result_queue = MPQueue()
        self.processes = []
        self.running = False
    
    def start(self):
        """启动进程池"""
        self.running = True
        for i in range(self.num_processes):
            p = Process(target=playwright_worker_process, 
                       args=(self.task_queue, self.result_queue, i))
            p.start()
            self.processes.append(p)
    
    def stop(self):
        """停止进程池"""
        self.running = False
        
        # 发送停止信号
        for _ in range(self.num_processes):
            self.task_queue.put(None)
        
        # 等待所有进程结束
        for p in self.processes:
            p.join()
    
    def execute_task(self, func_name: str, *args, **kwargs) -> Any:
        """执行任务"""
        task_id = threading.get_ident()
        self.task_queue.put((task_id, func_name, args, kwargs))
        
        # 等待结果
        while True:
            try:
                result_task_id, status, result = self.result_queue.get(timeout=10)
                if result_task_id == task_id:
                    if status == 'success':
                        return result
                    else:
                        raise Exception(result)
            except:
                raise TimeoutError("任务执行超时")

# ============================================================================
# 示例使用
# ============================================================================

def demo_playwright_threading_solutions():
    """演示Playwright跨线程解决方案"""
    
    print("=== 方案1: 单线程Playwright工作器 ===")
    
    # 方案1示例
    worker = PlaywrightWorker()
    worker.start()
    
    def multi_thread_scraping():
        """多线程爬取示例"""
        urls = [
            "https://example1.com",
            "https://example2.com", 
            "https://example3.com"
        ]
        
        def scrape_url(url):
            result = worker.execute_task(simulate_playwright_scrape, url)
            print(f"线程 {threading.current_thread().name} 完成: {result['url']}")
            return result
        
        # 使用线程池执行爬取任务
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(scrape_url, url) for url in urls]
            results = [future.result() for future in futures]
        
        return results
    
    results1 = multi_thread_scraping()
    worker.stop()
    
    print(f"方案1完成，爬取了 {len(results1)} 个页面")
    
    print("\n=== 方案3: 进程池Playwright ===")
    
    # 方案3示例
    process_pool = PlaywrightProcessPool(num_processes=2)
    process_pool.start()
    
    def multi_process_tasks():
        """多进程任务示例"""
        tasks = [
            ('scrape', 'https://process1.com'),
            ('scrape', 'https://process2.com'),
            ('screenshot', 'https://process3.com', 'screenshot1.png'),
            ('screenshot', 'https://process4.com', 'screenshot2.png'),
        ]
        
        def execute_task(task_info):
            func_name, *args = task_info
            result = process_pool.execute_task(func_name, *args)
            print(f"进程任务完成: {func_name} - {args[0]}")
            return result
        
        # 使用线程池提交进程任务
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(execute_task, task) for task in tasks]
            results = [future.result() for future in futures]
        
        return results
    
    results3 = multi_process_tasks()
    process_pool.stop()
    
    print(f"方案3完成，执行了 {len(results3)} 个任务")

async def demo_async_playwright():
    """演示异步Playwright管理器"""
    print("\n=== 方案2: 异步Playwright管理器 ===")
    
    manager = AsyncPlaywrightManager(max_workers=2)
    await manager.start()
    
    # 提交多个异步任务
    tasks = [
        manager.submit_task(simulate_playwright_scrape, f"https://async{i}.com")
        for i in range(5)
    ]
    
    results = await asyncio.gather(*tasks)
    
    await manager.stop()
    
    print(f"异步方案完成，处理了 {len(results)} 个任务")

# ============================================================================
# 真实Playwright示例（需要安装playwright）
# ============================================================================

def real_playwright_example():
    """真实的Playwright使用示例"""
    print("\n=== 真实Playwright示例 ===")
    
    # 注意：这需要安装playwright
    # pip install playwright
    # playwright install
    
    example_code = '''
from playwright.sync_api import sync_playwright
import threading
import queue

class RealPlaywrightWorker:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
    
    def start(self):
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop)
        self.worker_thread.start()
    
    def _worker_loop(self):
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            
            while self.running:
                try:
                    task = self.task_queue.get(timeout=1)
                    if task is None:
                        break
                    
                    task_id, action, args = task
                    
                    try:
                        if action == 'goto':
                            page.goto(args['url'])
                            result = {'title': page.title(), 'url': page.url}
                        elif action == 'screenshot':
                            page.goto(args['url'])
                            page.screenshot(path=args['path'])
                            result = {'path': args['path']}
                        
                        self.result_queue.put((task_id, 'success', result))
                        
                    except Exception as e:
                        self.result_queue.put((task_id, 'error', str(e)))
                        
                except queue.Empty:
                    continue
            
            browser.close()
    
    def execute_task(self, action, args):
        task_id = threading.get_ident()
        self.task_queue.put((task_id, action, args))
        
        while True:
            result_task_id, status, result = self.result_queue.get()
            if result_task_id == task_id:
                if status == 'success':
                    return result
                else:
                    raise Exception(result)

# 使用示例:
# worker = RealPlaywrightWorker()
# worker.start()
# 
# # 在其他线程中使用
# result = worker.execute_task('goto', {'url': 'https://example.com'})
# print(result)
# 
# worker.stop()
'''
    
    print("真实Playwright代码示例:")
    print(example_code)

def main():
    """主函数"""
    print("Playwright 跨线程解决方案演示")
    print("=" * 50)
    
    # 演示同步方案
    demo_playwright_threading_solutions()
    
    # 演示异步方案
    asyncio.run(demo_async_playwright())
    
    # 显示真实Playwright示例
    real_playwright_example()

if __name__ == "__main__":
    main()
