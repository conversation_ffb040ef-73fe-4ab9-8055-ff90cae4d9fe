import queue
import threading
import time

from bs4 import BeautifulSoup

import blog_spider
import random


def do_crawer(url_queue: queue.Queue, html_queue: queue.Queue):
    while True:
        url = url_queue.get()
        html = blog_spider.crawler(url)
        html_queue.put(html)
        print(f'crawer {url} finished,当前程序：{threading.current_thread().name}，队列大小：{url_queue.qsize()}')
        time.sleep(random.randint(1, 2))

def do_parser(html_queue: queue.Queue, fout):
    while True:
        html = html_queue.get()
        htmls = blog_spider.parser(html)
        for html in htmls:
            fout.write(str(html)+'\n')
        print(f'parser {url} finished,当前程序{threading.current_thread().name}，队列大小：{html_queue.qsize()}')
        time.sleep(random.randint(1, 2))

if __name__ == '__main__':
    url_queue = queue.Queue()
    html_queue = queue.Queue()

    for url in blog_spider.urls:
        url_queue.put(url)

    for i in range(3):
        t = threading.Thread(target=do_crawer, args=(url_queue, html_queue), name=f'crawer{i}')
        t.start()
    fout = open('crawler.data.txt','w',encoding='utf-8')
    for i in range(2):
        t = threading.Thread(target=do_parser, args=(html_queue,fout), name=f'parser{i}')
        t.start()

    print('over')