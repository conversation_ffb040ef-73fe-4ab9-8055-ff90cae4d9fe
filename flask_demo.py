import json

import flask
import time
from concurrent.futures import ThreadPoolExecutor
app = flask.Flask(__name__)
pool = ThreadPoolExecutor()



def read_file():
    time.sleep(0.1)
    return "return_file"


def read_api():
    time.sleep(0.2)
    return "return_api"


def read_db():
    time.sleep(0.3)
    return "return_db"


@app.route('/')
def index():
    future_file = pool.submit(read_file)
    future_db = pool.submit(read_db)
    future_api = pool.submit(read_api)

    # 使用字符串作为JSON键，并获取future结果
    return json.dumps({
        "file_result": future_file.result(),
        "db_result": future_db.result(),
        "api_result": future_api.result()
    })


if __name__ == '__main__':
    app.run()
