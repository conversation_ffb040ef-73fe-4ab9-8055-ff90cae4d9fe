"""
高级线程模式和最佳实践
包含线程本地存储、上下文管理器、装饰器等高级用法
"""

import threading
import time
import functools
import contextlib
from typing import Any, Callable, Dict, Optional
import weakref
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(threadName)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 1. 线程本地存储 (Thread Local Storage)
# ============================================================================

# 全局线程本地存储
thread_local_data = threading.local()

def thread_local_example():
    """线程本地存储示例"""
    print("\n=== 线程本地存储示例 ===")
    
    def worker(name: str, value: int):
        """工作函数，使用线程本地存储"""
        # 每个线程都有自己的数据副本
        thread_local_data.name = name
        thread_local_data.value = value
        thread_local_data.counter = 0
        
        logger.info(f"线程 {name} 初始化，值: {value}")
        
        for i in range(3):
            thread_local_data.counter += 1
            logger.info(f"线程 {name}: counter={thread_local_data.counter}, value={thread_local_data.value}")
            time.sleep(0.5)
        
        logger.info(f"线程 {name} 完成")
    
    # 创建多个线程
    threads = []
    for i in range(3):
        t = threading.Thread(target=worker, args=(f"Worker-{i}", i * 10))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()

class ThreadLocalManager:
    """线程本地存储管理器"""
    
    def __init__(self):
        self._local = threading.local()
    
    def set_context(self, **kwargs):
        """设置线程上下文"""
        for key, value in kwargs.items():
            setattr(self._local, key, value)
    
    def get_context(self, key: str, default=None):
        """获取线程上下文"""
        return getattr(self._local, key, default)
    
    def clear_context(self):
        """清除线程上下文"""
        self._local.__dict__.clear()

# ============================================================================
# 2. 线程装饰器
# ============================================================================

def threaded(func: Callable) -> Callable:
    """将函数装饰为在新线程中运行"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.start()
        return thread
    return wrapper

def synchronized(lock: threading.Lock = None):
    """同步装饰器，确保函数线程安全"""
    if lock is None:
        lock = threading.Lock()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with lock:
                return func(*args, **kwargs)
        return wrapper
    return decorator

def timeout(seconds: float):
    """超时装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = [None]
            exception = [None]
            
            def target():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=target)
            thread.start()
            thread.join(timeout=seconds)
            
            if thread.is_alive():
                raise TimeoutError(f"函数执行超时 ({seconds}秒)")
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        return wrapper
    return decorator

def decorator_examples():
    """装饰器示例"""
    print("\n=== 线程装饰器示例 ===")
    
    # 线程装饰器示例
    @threaded
    def background_task(name: str, duration: int):
        logger.info(f"后台任务 {name} 开始")
        time.sleep(duration)
        logger.info(f"后台任务 {name} 完成")
    
    # 启动后台任务
    thread1 = background_task("Task1", 2)
    thread2 = background_task("Task2", 1)
    
    # 同步装饰器示例
    shared_resource = []
    resource_lock = threading.Lock()
    
    @synchronized(resource_lock)
    def add_to_resource(item):
        logger.info(f"添加 {item} 到共享资源")
        shared_resource.append(item)
        time.sleep(0.1)  # 模拟处理时间
    
    # 多线程访问同步资源
    threads = []
    for i in range(5):
        t = threading.Thread(target=add_to_resource, args=(f"item-{i}",))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    logger.info(f"共享资源内容: {shared_resource}")
    
    # 超时装饰器示例
    @timeout(2.0)
    def slow_function():
        time.sleep(3)  # 会超时
        return "完成"
    
    @timeout(2.0)
    def fast_function():
        time.sleep(1)  # 不会超时
        return "快速完成"
    
    try:
        result = fast_function()
        logger.info(f"快速函数结果: {result}")
    except TimeoutError as e:
        logger.error(f"快速函数超时: {e}")
    
    try:
        result = slow_function()
        logger.info(f"慢速函数结果: {result}")
    except TimeoutError as e:
        logger.error(f"慢速函数超时: {e}")
    
    # 等待后台任务完成
    thread1.join()
    thread2.join()

# ============================================================================
# 3. 上下文管理器
# ============================================================================

class ThreadPoolContext:
    """线程池上下文管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.threads = []
        self.task_queue = None
        self.running = False
    
    def __enter__(self):
        """进入上下文"""
        import queue
        self.task_queue = queue.Queue()
        self.running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            t = threading.Thread(target=self._worker, args=(f"Worker-{i}",))
            t.start()
            self.threads.append(t)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        self.running = False
        
        # 发送停止信号
        for _ in range(self.max_workers):
            self.task_queue.put(None)
        
        # 等待所有线程完成
        for t in self.threads:
            t.join()
    
    def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"{worker_name} 启动")
        
        while self.running:
            try:
                task = self.task_queue.get(timeout=1)
                if task is None:  # 停止信号
                    break
                
                func, args, kwargs = task
                try:
                    result = func(*args, **kwargs)
                    logger.info(f"{worker_name} 完成任务，结果: {result}")
                except Exception as e:
                    logger.error(f"{worker_name} 任务失败: {e}")
                
            except:
                continue
        
        logger.info(f"{worker_name} 结束")
    
    def submit(self, func: Callable, *args, **kwargs):
        """提交任务"""
        if self.running:
            self.task_queue.put((func, args, kwargs))

@contextlib.contextmanager
def thread_timer(name: str):
    """线程计时上下文管理器"""
    start_time = time.time()
    thread_name = threading.current_thread().name
    logger.info(f"[{thread_name}] {name} 开始")
    
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"[{thread_name}] {name} 完成，耗时: {duration:.2f}秒")

def context_manager_examples():
    """上下文管理器示例"""
    print("\n=== 上下文管理器示例 ===")
    
    # 使用自定义线程池
    with ThreadPoolContext(max_workers=3) as pool:
        # 提交任务
        for i in range(8):
            pool.submit(lambda x: time.sleep(1) or f"Task-{x} completed", i)
        
        time.sleep(5)  # 等待任务完成
    
    # 使用计时上下文管理器
    def timed_task(name: str, duration: float):
        with thread_timer(f"任务 {name}"):
            time.sleep(duration)
    
    threads = []
    for i in range(3):
        t = threading.Thread(target=timed_task, args=(f"TimedTask-{i}", i + 1))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()

# ============================================================================
# 4. 线程池监控和管理
# ============================================================================

class MonitoredThreadPool:
    """可监控的线程池"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.threads = []
        self.task_queue = None
        self.running = False
        self.stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'active_threads': 0
        }
        self.stats_lock = threading.Lock()
    
    def start(self):
        """启动线程池"""
        import queue
        self.task_queue = queue.Queue()
        self.running = True
        
        for i in range(self.max_workers):
            t = threading.Thread(target=self._worker, args=(f"Worker-{i}",))
            t.start()
            self.threads.append(t)
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitor)
        monitor_thread.start()
        self.threads.append(monitor_thread)
    
    def stop(self):
        """停止线程池"""
        self.running = False
        
        for _ in range(self.max_workers):
            self.task_queue.put(None)
        
        for t in self.threads:
            t.join()
    
    def _worker(self, worker_name: str):
        """工作线程"""
        while self.running:
            try:
                task = self.task_queue.get(timeout=1)
                if task is None:
                    break
                
                with self.stats_lock:
                    self.stats['active_threads'] += 1
                
                func, args, kwargs = task
                try:
                    result = func(*args, **kwargs)
                    with self.stats_lock:
                        self.stats['tasks_completed'] += 1
                except Exception as e:
                    with self.stats_lock:
                        self.stats['tasks_failed'] += 1
                    logger.error(f"{worker_name} 任务失败: {e}")
                finally:
                    with self.stats_lock:
                        self.stats['active_threads'] -= 1
                
            except:
                continue
    
    def _monitor(self):
        """监控线程"""
        while self.running:
            time.sleep(2)
            with self.stats_lock:
                stats_copy = self.stats.copy()
            
            logger.info(f"线程池状态: {stats_copy}")
    
    def submit(self, func: Callable, *args, **kwargs):
        """提交任务"""
        if self.running:
            with self.stats_lock:
                self.stats['tasks_submitted'] += 1
            self.task_queue.put((func, args, kwargs))
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        with self.stats_lock:
            return self.stats.copy()

def monitoring_example():
    """监控示例"""
    print("\n=== 线程池监控示例 ===")
    
    pool = MonitoredThreadPool(max_workers=2)
    pool.start()
    
    def sample_task(task_id: int):
        """示例任务"""
        duration = task_id % 3 + 1
        time.sleep(duration)
        if task_id % 5 == 0:  # 模拟失败
            raise Exception(f"Task {task_id} failed")
        return f"Task {task_id} completed"
    
    # 提交任务
    for i in range(10):
        pool.submit(sample_task, i)
    
    # 等待一段时间
    time.sleep(8)
    
    # 获取最终统计
    final_stats = pool.get_stats()
    logger.info(f"最终统计: {final_stats}")
    
    pool.stop()

# ============================================================================
# 5. 线程安全的单例模式
# ============================================================================

class ThreadSafeSingleton:
    """线程安全的单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self.data = {}
                    self.counter = 0
                    self._initialized = True
    
    def increment(self):
        """线程安全的计数器"""
        with self._lock:
            self.counter += 1
            return self.counter
    
    def set_data(self, key: str, value: Any):
        """线程安全的数据设置"""
        with self._lock:
            self.data[key] = value
    
    def get_data(self, key: str, default=None):
        """线程安全的数据获取"""
        with self._lock:
            return self.data.get(key, default)

def singleton_example():
    """单例模式示例"""
    print("\n=== 线程安全单例模式示例 ===")
    
    def worker(worker_id: int):
        """工作函数"""
        singleton = ThreadSafeSingleton()
        
        for i in range(3):
            count = singleton.increment()
            singleton.set_data(f"worker_{worker_id}_item_{i}", f"data_{count}")
            logger.info(f"Worker {worker_id}: count={count}")
            time.sleep(0.1)
        
        # 读取数据
        all_data = {}
        for key in list(singleton.data.keys()):
            all_data[key] = singleton.get_data(key)
        
        logger.info(f"Worker {worker_id} 看到的数据: {len(all_data)} 项")
    
    # 创建多个线程
    threads = []
    for i in range(4):
        t = threading.Thread(target=worker, args=(i,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    # 验证单例
    s1 = ThreadSafeSingleton()
    s2 = ThreadSafeSingleton()
    logger.info(f"单例验证: s1 is s2 = {s1 is s2}")
    logger.info(f"最终计数器值: {s1.counter}")

# ============================================================================
# 主函数
# ============================================================================

def main():
    """运行所有高级示例"""
    print("Python 高级线程模式和最佳实践")
    print("=" * 50)
    
    thread_local_example()
    decorator_examples()
    context_manager_examples()
    monitoring_example()
    singleton_example()

if __name__ == "__main__":
    main()
