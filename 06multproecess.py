
from concurrent.futures import ThreadPoolExecutor,ProcessPoolExecutor
PRIMES=[112272535095293]*10
def is_prime(n):
    """判断一个数是否为素数"""
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    # 只需检查到平方根
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True

def single_handler():
    for number in PRIMES:
        is_prime(number)


def mult_thread():
    with ThreadPoolExecutor() as pool:
        pool.map(is_prime,PRIMES)

def mult_process():
    with ProcessPoolExecutor() as pool:
        pool.map(is_prime,PRIMES)


if __name__ == '__main__':
    import time
    start_time = time.time()
    single_handler()
    end_time = time.time()
    print(f'single_handler程序执行时间：{end_time - start_time}')
    start_time = time.time()
    mult_thread()
    end_time = time.time()
    print(f'mult_thread程序执行时间：{end_time - start_time}')

    start_time = time.time()
    mult_process()
    end_time = time.time()
    print(f'mult_process程序执行时间：{end_time - start_time}')